import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CompactProgress } from '@/components/ui/enhanced-progress';
import { useAdminWebSocket } from '@/hooks/useAdminWebSocket';
import { WebSocketConnectionStatus, ConnectionStatusDot } from '@/components/common/ConnectionStatusIndicator';
import {
  EmptyActiveTasksState,
  MonitorCardLoadingSkeleton
} from '@/components/common/StandardizedLoadingStates';
import {
  Activity,
  Database,
  Users,
  Clock,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  XCircle,
  Loader2,
  Eye,
  EyeOff,
  RefreshCw
} from 'lucide-react';
import { RealTimeProgressUpdate, Progress } from '@/types/scanLogs';

interface ActiveScan {
  scanId: string;
  repoId: string;
  groupName: string;
  startTime: Date;
  lastUpdate: Date;
  progress?: Progress;
  status: 'running' | 'completed' | 'failed';
  errorMessage?: string;
  scanType: 'manual' | 'auto' | 'scheduled';
}

interface GlobalScanMonitorProps {
  className?: string;
  maxDisplayedScans?: number;
  autoRefresh?: boolean;
}

export function GlobalScanMonitor({
  className = "",
  maxDisplayedScans = 10,
  autoRefresh = true
}: GlobalScanMonitorProps) {
  const [activeScans, setActiveScans] = useState<Map<string, ActiveScan>>(new Map());
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [totalScansToday, setTotalScansToday] = useState(0);
  const [completedScansToday, setCompletedScansToday] = useState(0);

  const {
    isConnected,
    isConnecting,
    error: wsError,
    lastUpdate,
    isAdminSubscribed,
    connect,
    disconnect,
    subscribeToAdminMonitoring,
    unsubscribeFromAdminMonitoring
  } = useAdminWebSocket({
    autoConnect: isMonitoring
  });

  // Subscribe to admin monitoring when connected and monitoring is enabled
  useEffect(() => {
    if (isConnected && isMonitoring && !isAdminSubscribed) {
      subscribeToAdminMonitoring();
    } else if (isAdminSubscribed && !isMonitoring) {
      unsubscribeFromAdminMonitoring();
    }
  }, [isConnected, isMonitoring, isAdminSubscribed, subscribeToAdminMonitoring, unsubscribeFromAdminMonitoring]);

  // Handle progress updates
  useEffect(() => {
    if (!lastUpdate) return;

    const update = lastUpdate;
    const scanId = update.scanId;

    setActiveScans(prev => {
      const newScans = new Map(prev);
      const existingScan = newScans.get(scanId);

      switch (update.eventType) {
        case 'scan_start':
          newScans.set(scanId, {
            scanId,
            repoId: update.repoId,
            groupName: update.groupName,
            startTime: new Date(update.timestamp),
            lastUpdate: new Date(update.timestamp),
            progress: update.progress,
            status: 'running',
            scanType: 'manual' // Default, could be enhanced with metadata
          });
          break;

        case 'progress':
        case 'file_progress':
        case 'chunk_start':
        case 'chunk_complete':
          if (existingScan) {
            newScans.set(scanId, {
              ...existingScan,
              lastUpdate: new Date(update.timestamp),
              progress: update.progress || existingScan.progress,
              status: 'running'
            });
          }
          break;

        case 'scan_complete':
          if (existingScan) {
            newScans.set(scanId, {
              ...existingScan,
              lastUpdate: new Date(update.timestamp),
              progress: update.progress || existingScan.progress,
              status: 'completed'
            });
            setCompletedScansToday(prev => prev + 1);
          }
          break;

        case 'scan_error':
          if (existingScan) {
            newScans.set(scanId, {
              ...existingScan,
              lastUpdate: new Date(update.timestamp),
              status: 'failed',
              errorMessage: update.error
            });
          }
          break;
      }

      return newScans;
    });
  }, [lastUpdate]);

  // Auto-cleanup completed/failed scans after 5 minutes
  useEffect(() => {
    const cleanup = setInterval(() => {
      const now = new Date();
      setActiveScans(prev => {
        const newScans = new Map(prev);
        for (const [scanId, scan] of newScans.entries()) {
          if (scan.status !== 'running') {
            const timeSinceUpdate = now.getTime() - scan.lastUpdate.getTime();
            if (timeSinceUpdate > 5 * 60 * 1000) { // 5 minutes
              newScans.delete(scanId);
            }
          }
        }
        return newScans;
      });
    }, 60000); // Check every minute

    return () => clearInterval(cleanup);
  }, []);

  const toggleMonitoring = () => {
    if (isMonitoring) {
      setIsMonitoring(false);
      if (isConnected) {
        const ws = (window as any).wsConnection;
        if (ws && ws.readyState === WebSocket.OPEN) {
          ws.send(JSON.stringify({ type: 'unsubscribe_admin' }));
        }
      }
    } else {
      setIsMonitoring(true);
      if (!isConnected) {
        connect();
      }
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'bg-blue-500';
      case 'completed':
        return 'bg-green-500';
      case 'failed':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const formatDuration = (startTime: Date, endTime?: Date) => {
    const end = endTime || new Date();
    const duration = end.getTime() - startTime.getTime();
    const minutes = Math.floor(duration / 60000);
    const seconds = Math.floor((duration % 60000) / 1000);

    if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    }
    return `${seconds}s`;
  };

  const activeScansArray = Array.from(activeScans.values())
    .sort((a, b) => b.lastUpdate.getTime() - a.lastUpdate.getTime())
    .slice(0, maxDisplayedScans);

  const runningScans = activeScansArray.filter(scan => scan.status === 'running');
  const completedScans = activeScansArray.filter(scan => scan.status === 'completed');
  const failedScans = activeScansArray.filter(scan => scan.status === 'failed');

  return (
    <Card className={`w-full ${className}`}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Global Scan Monitor
          </CardTitle>

          <div className="flex items-center gap-2">
            <Badge variant="outline" className="gap-1">
              <Database className="h-3 w-3" />
              {runningScans.length} active
            </Badge>

            <Button
              size="sm"
              variant={isMonitoring ? "default" : "outline"}
              onClick={toggleMonitoring}
              className="gap-1"
            >
              {isMonitoring ? <Eye className="h-3 w-3" /> : <EyeOff className="h-3 w-3" />}
              {isMonitoring ? 'Monitoring' : 'Start Monitor'}
            </Button>

            {!isConnected && !isConnecting && (
              <Button size="sm" variant="outline" onClick={connect}>
                <RefreshCw className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>

        {/* Connection Status */}
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <ConnectionStatusDot
            isConnected={isConnected}
            isConnecting={isConnecting}
            error={wsError}
          />

          <div className="flex items-center gap-1">
            <TrendingUp className="h-3 w-3" />
            {completedScansToday} completed today
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Error Alert */}
        {wsError && (
          <Alert className="border-red-200 bg-red-50">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">{wsError}</AlertDescription>
          </Alert>
        )}

        {/* Active Scans */}
        {runningScans.length > 0 && (
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
              Running Scans ({runningScans.length})
            </h4>

            {runningScans.map(scan => (
              <div key={scan.scanId} className="border rounded-lg p-3 bg-blue-50/50">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(scan.status)}
                    <span className="font-medium text-sm">
                      {scan.groupName} @ {scan.repoId}
                    </span>
                  </div>

                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <Clock className="h-3 w-3" />
                    {formatDuration(scan.startTime)}
                  </div>
                </div>

                {scan.progress && (
                  <CompactProgress progress={scan.progress} className="mt-2" />
                )}
              </div>
            ))}
          </div>
        )}

        {/* Recent Completed/Failed Scans */}
        {(completedScans.length > 0 || failedScans.length > 0) && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-gray-700">Recent Activity</h4>

            {[...completedScans, ...failedScans].map(scan => (
              <div key={scan.scanId} className="flex items-center justify-between p-2 border rounded text-sm">
                <div className="flex items-center gap-2">
                  {getStatusIcon(scan.status)}
                  <span>{scan.groupName} @ {scan.repoId}</span>
                </div>

                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <Clock className="h-3 w-3" />
                  {formatDuration(scan.startTime, scan.lastUpdate)}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Empty State */}
        {activeScansArray.length === 0 && isMonitoring && (
          <EmptyActiveTasksState
            title="No Active Scans"
            description="Monitoring for new scan activity..."
            icon={Activity}
          />
        )}

        {!isMonitoring && (
          <EmptyActiveTasksState
            title="Monitoring Disabled"
            description='Click "Start Monitor" to begin tracking scans'
            icon={EyeOff}
          />
        )}
      </CardContent>
    </Card>
  );
}
