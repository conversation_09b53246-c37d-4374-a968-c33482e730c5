package models

import (
	"encoding/json"
	"fmt"
	"time"
)

// AutoScanConfig represents the configuration for automated scanning
type AutoScanConfig struct {
	ID                 string              `json:"id"`
	RepoID             string              `json:"repoId"`
	Enabled            bool                `json:"enabled"`
	Frequency          string              `json:"frequency"`          // "daily", "weekly", "monthly"
	TimeWindow         TimeWindow          `json:"timeWindow"`         // When scans can run
	MaxConcurrentScans int                 `json:"maxConcurrentScans"` // Max concurrent scans
	ScanAllGroups      bool                `json:"scanAllGroups"`      // Scan all groups or specific ones
	TargetGroups       []string            `json:"targetGroups"`       // Specific groups to scan (if not all)
	LoadBalancing      LoadBalancingConfig `json:"loadBalancing"`      // Load balancing settings
	NotificationConfig NotificationConfig  `json:"notificationConfig"` // Notification settings
	CreatedAt          time.Time           `json:"createdAt"`
	UpdatedAt          time.Time           `json:"updatedAt"`
	LastScanTime       *time.Time          `json:"lastScanTime,omitempty"`
	NextScanTime       *time.Time          `json:"nextScanTime,omitempty"`
}

// TimeWindow defines when scans can run
type TimeWindow struct {
	StartHour int    `json:"startHour"` // 0-23
	EndHour   int    `json:"endHour"`   // 0-23
	Timezone  string `json:"timezone"`  // e.g., "UTC", "America/New_York"
}

// LoadBalancingConfig defines how scans are distributed
type LoadBalancingConfig struct {
	SpreadAcrossDay    bool          `json:"spreadAcrossDay"`    // Spread scans across the day
	MinIntervalBetween time.Duration `json:"minIntervalBetween"` // Minimum time between scans
	MaxScansPerHour    int           `json:"maxScansPerHour"`    // Maximum scans per hour
}

// NotificationConfig defines notification settings
type NotificationConfig struct {
	OnCompletion bool `json:"onCompletion"` // Notify when scan completes
	OnFailure    bool `json:"onFailure"`    // Notify when scan fails
	OnSummary    bool `json:"onSummary"`    // Send daily/weekly summary
}

// AutoScanStatus represents the current status of automated scanning
type AutoScanStatus struct {
	ConfigID            string        `json:"configId"`
	RepoID              string        `json:"repoId"`
	IsActive            bool          `json:"isActive"`
	LastScanTime        *time.Time    `json:"lastScanTime,omitempty"`
	NextScanTime        *time.Time    `json:"nextScanTime,omitempty"`
	TotalScansRun       int           `json:"totalScansRun"`
	SuccessfulScans     int           `json:"successfulScans"`
	FailedScans         int           `json:"failedScans"`
	AverageScanduration time.Duration `json:"averageScanDuration"`
	CurrentlyScanning   []string      `json:"currentlyScanning"` // Groups currently being scanned
}

// AutoScanJob represents a scheduled scan job
type AutoScanJob struct {
	ID           string     `json:"id"`
	ConfigID     string     `json:"configId"`
	RepoID       string     `json:"repoId"`
	GroupName    string     `json:"groupName"`
	ScheduledFor time.Time  `json:"scheduledFor"`
	Status       string     `json:"status"` // "pending", "running", "completed", "failed"
	CreatedAt    time.Time  `json:"createdAt"`
	StartedAt    *time.Time `json:"startedAt,omitempty"`
	CompletedAt  *time.Time `json:"completedAt,omitempty"`
	Error        string     `json:"error,omitempty"`
	ResultsCount int        `json:"resultsCount"`
}

// AutoScanSummary represents a summary of automated scanning activity
type AutoScanSummary struct {
	RepoID            string          `json:"repoId"`
	Period            string          `json:"period"` // "daily", "weekly", "monthly"
	StartTime         time.Time       `json:"startTime"`
	EndTime           time.Time       `json:"endTime"`
	TotalJobs         int             `json:"totalJobs"`
	CompletedJobs     int             `json:"completedJobs"`
	FailedJobs        int             `json:"failedJobs"`
	TotalResults      int             `json:"totalResults"`
	AverageDuration   time.Duration   `json:"averageDuration"`
	GroupsScaneed     []string        `json:"groupsScanned"`
	TopFailureReasons []FailureReason `json:"topFailureReasons"`
}

// FailureReason represents a common failure reason
type FailureReason struct {
	Reason string `json:"reason"`
	Count  int    `json:"count"`
}

// Validation methods
func (c *AutoScanConfig) Validate() error {
	if c.RepoID == "" {
		return fmt.Errorf("repoId is required")
	}

	if c.Frequency != "daily" && c.Frequency != "weekly" && c.Frequency != "monthly" {
		return fmt.Errorf("frequency must be 'daily', 'weekly', or 'monthly'")
	}

	if c.TimeWindow.StartHour < 0 || c.TimeWindow.StartHour > 23 {
		return fmt.Errorf("startHour must be between 0 and 23")
	}

	if c.TimeWindow.EndHour < 0 || c.TimeWindow.EndHour > 23 {
		return fmt.Errorf("endHour must be between 0 and 23")
	}

	if c.MaxConcurrentScans < 1 {
		return fmt.Errorf("maxConcurrentScans must be at least 1")
	}

	if !c.ScanAllGroups && len(c.TargetGroups) == 0 {
		return fmt.Errorf("targetGroups must be specified when scanAllGroups is false")
	}

	return nil
}

// Helper methods
func (c *AutoScanConfig) IsInTimeWindow(t time.Time) bool {
	if c.TimeWindow.Timezone != "" {
		if loc, err := time.LoadLocation(c.TimeWindow.Timezone); err == nil {
			t = t.In(loc)
		}
	}

	hour := t.Hour()
	if c.TimeWindow.StartHour <= c.TimeWindow.EndHour {
		return hour >= c.TimeWindow.StartHour && hour <= c.TimeWindow.EndHour
	} else {
		// Handles cases like 22:00 to 06:00 (overnight)
		return hour >= c.TimeWindow.StartHour || hour <= c.TimeWindow.EndHour
	}
}

func (c *AutoScanConfig) CalculateNextScanTime(from time.Time) time.Time {
	var next time.Time

	switch c.Frequency {
	case "daily":
		next = from.Add(24 * time.Hour)
	case "weekly":
		next = from.Add(7 * 24 * time.Hour)
	case "monthly":
		next = from.AddDate(0, 1, 0)
	default:
		next = from.Add(24 * time.Hour) // Default to daily
	}

	// Adjust to time window if necessary
	if !c.IsInTimeWindow(next) {
		// Move to start of next time window
		for !c.IsInTimeWindow(next) {
			next = next.Add(time.Hour)
		}
	}

	return next
}

// JSON marshaling helpers
func (c *AutoScanConfig) ToJSON() ([]byte, error) {
	return json.Marshal(c)
}

func (c *AutoScanConfig) FromJSON(data []byte) error {
	return json.Unmarshal(data, c)
}
