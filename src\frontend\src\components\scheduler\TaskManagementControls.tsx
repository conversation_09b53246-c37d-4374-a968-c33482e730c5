import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import {
  Play,
  Pause,
  Square,
  RotateCcw,
  Zap,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Settings,
  Power
} from 'lucide-react';

import api from '@/api/client';
import type {
  SchedulerStatus,
  ServiceStatus,
  TaskControlRequest,
  TaskControlResponse
} from '@/types/scheduler';
import {
  ServiceStatusColors,
  getServiceDisplayName
} from '@/types/scheduler';

interface TaskManagementControlsProps {
  schedulerStatus?: SchedulerStatus;
  onStatusUpdate?: () => void;
}

const TaskManagementControls: React.FC<TaskManagementControlsProps> = ({
  schedulerStatus,
  onStatusUpdate
}) => {
  const [loading, setLoading] = useState<string | null>(null);
  const [lastAction, setLastAction] = useState<{ action: string; result: TaskControlResponse } | null>(null);

  // Handle control action
  const handleControlAction = async (action: 'pause' | 'resume' | 'trigger' | 'retry', service: 'all' | 'report' | 'usage' | 'auto-scan' | 'worker-pool' = 'all') => {
    try {
      setLoading(`${action}-${service}`);
      setLastAction(null);

      const request: TaskControlRequest = {
        action,
        service,
      };

      let response: TaskControlResponse;

      switch (action) {
        case 'pause':
          response = await api.scheduler.pauseScheduler(request);
          break;
        case 'resume':
          response = await api.scheduler.resumeScheduler(request);
          break;
        case 'trigger':
          response = await api.scheduler.triggerTask(request);
          break;
        case 'retry':
          response = await api.scheduler.retryTask(request);
          break;
        default:
          throw new Error(`Unknown action: ${action}`);
      }

      setLastAction({ action: `${action} ${service}`, result: response });

      // Refresh status after action
      if (onStatusUpdate) {
        setTimeout(onStatusUpdate, 1000);
      }
    } catch (error) {
      console.error(`Failed to ${action} ${service}:`, error);
      setLastAction({
        action: `${action} ${service}`,
        result: {
          success: false,
          message: `Failed to ${action} ${service}: ${error instanceof Error ? error.message : 'Unknown error'}`
        }
      });
    } finally {
      setLoading(null);
    }
  };

  // Service control component
  const ServiceControl = ({ service, status }: { service: 'all' | 'report' | 'usage' | 'auto-scan' | 'worker-pool'; status: ServiceStatus }) => (
    <div className="border rounded-lg p-4">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          <h3 className="font-medium">{getServiceDisplayName(status.name)}</h3>
          <Badge className={`${ServiceStatusColors[status.status]} border-0`}>
            {status.status}
          </Badge>
          {status.isRunning && (
            <Badge variant="outline" className="text-green-600">
              Running
            </Badge>
          )}
        </div>
        <div className="flex items-center space-x-2">
          {status.status === 'running' ? (
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleControlAction('pause', service)}
              disabled={loading === `pause-${service}`}
            >
              <Pause className="h-4 w-4 mr-1" />
              Pause
            </Button>
          ) : (
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleControlAction('resume', service)}
              disabled={loading === `resume-${service}`}
            >
              <Play className="h-4 w-4 mr-1" />
              Resume
            </Button>
          )}

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <Settings className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => handleControlAction('trigger', service)}>
                <Zap className="h-4 w-4 mr-2" />
                Trigger Now
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleControlAction('retry', service)}>
                <RotateCcw className="h-4 w-4 mr-2" />
                Retry Failed
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
        <div>
          <span className="font-medium">Active Tasks:</span> {status.activeTasks}
        </div>
        <div>
          <span className="font-medium">Queued Tasks:</span> {status.queuedTasks}
        </div>
        <div className="col-span-2">
          <span className="font-medium">Last Activity:</span> {new Date(status.lastActivity).toLocaleString()}
        </div>
        {status.errorMessage && (
          <div className="col-span-2">
            <span className="font-medium text-red-600">Error:</span> {status.errorMessage}
          </div>
        )}
      </div>
    </div>
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Power className="h-5 w-5" />
          <span>Scheduler Management Controls</span>
        </CardTitle>
        <CardDescription>
          Control and manage individual scheduler services
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Last Action Result */}
        {lastAction && (
          <Alert className={lastAction.result.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
            <div className="flex items-center">
              {lastAction.result.success ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <XCircle className="h-4 w-4 text-red-600" />
              )}
              <AlertDescription className="ml-2">
                <strong>{lastAction.action}:</strong> {lastAction.result.message}
              </AlertDescription>
            </div>
          </Alert>
        )}

        {/* Global Controls */}
        <div className="border rounded-lg p-4 bg-gray-50">
          <h3 className="font-medium mb-3">Global Controls</h3>
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              onClick={() => handleControlAction('pause', 'all')}
              disabled={loading === 'pause-all'}
              className="flex items-center"
            >
              <Pause className="h-4 w-4 mr-2" />
              Pause All Services
            </Button>

            <Button
              variant="outline"
              onClick={() => handleControlAction('resume', 'all')}
              disabled={loading === 'resume-all'}
              className="flex items-center"
            >
              <Play className="h-4 w-4 mr-2" />
              Resume All Services
            </Button>

            <Button
              variant="outline"
              onClick={() => handleControlAction('trigger', 'all')}
              disabled={loading === 'trigger-all'}
              className="flex items-center"
            >
              <Zap className="h-4 w-4 mr-2" />
              Trigger All Pending
            </Button>
          </div>
        </div>

        {/* Individual Service Controls */}
        {schedulerStatus ? (
          <div className="space-y-4">
            <h3 className="font-medium">Individual Service Controls</h3>

            <ServiceControl
              service="report"
              status={schedulerStatus.reportScheduler}
            />

            <ServiceControl
              service="usage"
              status={schedulerStatus.usageScanner}
            />

            <ServiceControl
              service="auto-scan"
              status={schedulerStatus.autoScanService}
            />

            <ServiceControl
              service="worker-pool"
              status={schedulerStatus.workerPool}
            />
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
            <p>Scheduler status not available</p>
            <p className="text-sm">Load the scheduler status to enable individual service controls</p>
          </div>
        )}

        {/* Emergency Controls */}
        <div className="border-2 border-red-200 rounded-lg p-4 bg-red-50">
          <h3 className="font-medium text-red-800 mb-3 flex items-center">
            <AlertTriangle className="h-4 w-4 mr-2" />
            Emergency Controls
          </h3>
          <div className="flex items-center space-x-4">
            <Button
              variant="destructive"
              size="sm"
              onClick={() => handleControlAction('pause', 'all')}
              disabled={loading === 'pause-all'}
            >
              <Square className="h-4 w-4 mr-2" />
              Emergency Stop All
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => handleControlAction('retry', 'all')}
              disabled={loading === 'retry-all'}
              className="border-red-300 text-red-700 hover:bg-red-100"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Retry All Failed
            </Button>
          </div>
          <p className="text-xs text-red-600 mt-2">
            Use these controls only in emergency situations. They will affect all running tasks.
          </p>
        </div>

        {/* Status Information */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-medium text-blue-800 mb-2">Control Information</h3>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• <strong>Pause:</strong> Temporarily stops the service from processing new tasks</li>
            <li>• <strong>Resume:</strong> Restarts a paused service</li>
            <li>• <strong>Trigger:</strong> Manually starts pending tasks immediately</li>
            <li>• <strong>Retry:</strong> Attempts to retry failed tasks</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

export default TaskManagementControls;
