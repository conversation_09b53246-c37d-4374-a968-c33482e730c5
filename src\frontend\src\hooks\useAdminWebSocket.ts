import { useState, useEffect, useRef, useCallback } from 'react';
import { RealTimeProgressUpdate } from '@/types/scanLogs';
import { API_CONFIG } from '@/configs';

interface UseAdminWebSocketOptions {
  url?: string;
  autoConnect?: boolean;
  reconnectAttempts?: number;
  reconnectInterval?: number;
}

interface AdminWebSocketState {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  lastUpdate: RealTimeProgressUpdate | null;
  allUpdates: RealTimeProgressUpdate[];
  isAdminSubscribed: boolean;
}

export function useAdminWebSocket(options: UseAdminWebSocketOptions = {}) {
  // Determine WebSocket protocol based on current page protocol
  const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
  // Use the configured WebSocket base URL which will be proxied to the backend
  const defaultUrl = `${wsProtocol}//${window.location.host}${API_CONFIG.WS_BASE_URL}/progress`;

  const {
    url = defaultUrl,
    autoConnect = true,
    reconnectAttempts = 5,
    reconnectInterval = 3000
  } = options;

  const [state, setState] = useState<AdminWebSocketState>({
    isConnected: false,
    isConnecting: false,
    error: null,
    lastUpdate: null,
    allUpdates: [],
    isAdminSubscribed: false
  });

  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectCountRef = useRef(0);

  // Clear reconnect timeout
  const clearReconnectTimeout = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
  }, []);

  // Send message to WebSocket
  const sendMessage = useCallback((message: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
      return true;
    }
    return false;
  }, []);

  // Connect to WebSocket
  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    setState(prev => ({ ...prev, isConnecting: true, error: null }));

    try {
      const clientId = `admin-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      const wsUrl = `${url}?clientId=${clientId}`;

      console.log('Attempting to connect to WebSocket:', wsUrl);
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log('Admin WebSocket connected to:', wsUrl);
        setState(prev => ({
          ...prev,
          isConnected: true,
          isConnecting: false,
          error: null
        }));
        reconnectCountRef.current = 0;

        // Auto-subscribe to admin monitoring if previously subscribed
        if (state.isAdminSubscribed) {
          subscribeToAdminMonitoring();
        }
      };

      wsRef.current.onmessage = (event) => {
        try {
          const update: RealTimeProgressUpdate = JSON.parse(event.data);

          setState(prev => ({
            ...prev,
            lastUpdate: update,
            allUpdates: [...prev.allUpdates.slice(-199), update] // Keep last 200 updates
          }));
        } catch (error) {
          console.error('Failed to parse admin WebSocket message:', error);
        }
      };

      wsRef.current.onclose = (event) => {
        console.log('Admin WebSocket disconnected:', event.code, event.reason);
        setState(prev => ({
          ...prev,
          isConnected: false,
          isConnecting: false
        }));

        // Attempt to reconnect if not a normal closure
        if (event.code !== 1000 && reconnectCountRef.current < reconnectAttempts) {
          reconnectCountRef.current++;
          console.log(`Attempting admin reconnect (${reconnectCountRef.current}/${reconnectAttempts})...`);

          reconnectTimeoutRef.current = setTimeout(() => {
            connect();
          }, reconnectInterval);
        }
      };

      wsRef.current.onerror = (error) => {
        console.error('Admin WebSocket error:', error);
        console.error('Failed to connect to:', wsUrl);
        setState(prev => ({
          ...prev,
          error: `WebSocket connection failed: ${wsUrl}`,
          isConnecting: false
        }));
      };

    } catch (error) {
      console.error('Failed to create admin WebSocket connection:', error);
      setState(prev => ({
        ...prev,
        error: 'Failed to create admin WebSocket connection',
        isConnecting: false
      }));
    }
  }, [url, reconnectAttempts, reconnectInterval, state.isAdminSubscribed]);

  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    clearReconnectTimeout();

    if (wsRef.current) {
      wsRef.current.close(1000, 'Manual disconnect');
      wsRef.current = null;
    }

    setState(prev => ({
      ...prev,
      isConnected: false,
      isConnecting: false,
      isAdminSubscribed: false
    }));
  }, [clearReconnectTimeout]);

  // Subscribe to admin monitoring (all scans)
  const subscribeToAdminMonitoring = useCallback(() => {
    if (sendMessage({ type: 'subscribe_admin' })) {
      setState(prev => ({ ...prev, isAdminSubscribed: true }));
      console.log('Subscribed to admin monitoring');
      return true;
    }
    return false;
  }, [sendMessage]);

  // Unsubscribe from admin monitoring
  const unsubscribeFromAdminMonitoring = useCallback(() => {
    if (sendMessage({ type: 'unsubscribe_admin' })) {
      setState(prev => ({ ...prev, isAdminSubscribed: false }));
      console.log('Unsubscribed from admin monitoring');
      return true;
    }
    return false;
  }, [sendMessage]);

  // Subscribe to specific repository
  const subscribeToRepository = useCallback((repoId: string) => {
    return sendMessage({ type: 'subscribe_repo', repoId });
  }, [sendMessage]);

  // Unsubscribe from specific repository
  const unsubscribeFromRepository = useCallback((repoId: string) => {
    return sendMessage({ type: 'unsubscribe_repo', repoId });
  }, [sendMessage]);

  // Subscribe to specific group
  const subscribeToGroup = useCallback((groupName: string) => {
    return sendMessage({ type: 'subscribe_group', groupName });
  }, [sendMessage]);

  // Unsubscribe from specific group
  const unsubscribeFromGroup = useCallback((groupName: string) => {
    return sendMessage({ type: 'unsubscribe_group', groupName });
  }, [sendMessage]);

  // Get updates for a specific scan
  const getUpdatesForScan = useCallback((scanId: string): RealTimeProgressUpdate[] => {
    return state.allUpdates.filter(update => update.scanId === scanId);
  }, [state.allUpdates]);

  // Get updates for a specific repository
  const getUpdatesForRepository = useCallback((repoId: string): RealTimeProgressUpdate[] => {
    return state.allUpdates.filter(update => update.repoId === repoId);
  }, [state.allUpdates]);

  // Get updates for a specific group
  const getUpdatesForGroup = useCallback((groupName: string): RealTimeProgressUpdate[] => {
    return state.allUpdates.filter(update => update.groupName === groupName);
  }, [state.allUpdates]);

  // Get latest update for a specific scan
  const getLatestUpdateForScan = useCallback((scanId: string): RealTimeProgressUpdate | null => {
    const scanUpdates = getUpdatesForScan(scanId);
    return scanUpdates.length > 0 ? scanUpdates[scanUpdates.length - 1] : null;
  }, [getUpdatesForScan]);

  // Clear all updates
  const clearUpdates = useCallback(() => {
    setState(prev => ({
      ...prev,
      allUpdates: [],
      lastUpdate: null
    }));
  }, []);

  // Get active scans (scans that have recent updates)
  const getActiveScans = useCallback((timeWindowMs: number = 5 * 60 * 1000): string[] => {
    const now = new Date().getTime();
    const recentUpdates = state.allUpdates.filter(update => {
      const updateTime = new Date(update.timestamp).getTime();
      return (now - updateTime) <= timeWindowMs;
    });

    const activeScanIds = new Set<string>();
    recentUpdates.forEach(update => {
      if (update.eventType !== 'scan_complete' && update.eventType !== 'scan_error') {
        activeScanIds.add(update.scanId);
      }
    });

    return Array.from(activeScanIds);
  }, [state.allUpdates]);

  // Auto-connect on mount
  useEffect(() => {
    if (autoConnect) {
      connect();
    }

    return () => {
      clearReconnectTimeout();
      if (wsRef.current) {
        wsRef.current.close(1000, 'Component unmount');
      }
    };
  }, [autoConnect, connect, clearReconnectTimeout]);

  return {
    // Connection state
    isConnected: state.isConnected,
    isConnecting: state.isConnecting,
    error: state.error,

    // Subscription state
    isAdminSubscribed: state.isAdminSubscribed,

    // Progress data
    lastUpdate: state.lastUpdate,
    allUpdates: state.allUpdates,

    // Connection control
    connect,
    disconnect,

    // Admin subscription control
    subscribeToAdminMonitoring,
    unsubscribeFromAdminMonitoring,

    // Specific subscription control
    subscribeToRepository,
    unsubscribeFromRepository,
    subscribeToGroup,
    unsubscribeFromGroup,

    // Data utilities
    clearUpdates,
    getUpdatesForScan,
    getUpdatesForRepository,
    getUpdatesForGroup,
    getLatestUpdateForScan,
    getActiveScans,

    // Connection info
    reconnectCount: reconnectCountRef.current
  };
}
