[{"id": "preset_1753196664278432100", "name": "Test Preset", "description": "Test preset description", "reportType": "users", "query": {"lob": "Marketing", "types": ["security"], "flattenMembership": false}, "searchQuery": "", "createdAt": "2025-07-22T17:04:24+02:00", "updatedAt": "2025-07-22T17:04:24+02:00", "isActive": true, "version": 1, "parentId": "preset_1753196664278432100", "sharedId": "shared_1753196664278432100", "schedule": {"enabled": false, "frequency": "", "dayOfWeek": 0, "dayOfMonth": 0, "hour": 0, "minute": 0, "intervalHours": 0, "intervalMinutes": 0, "nextRun": ""}, "repositoryId": ""}, {"id": "preset_1753196664301089900", "name": "Updated Name", "description": "Test preset description", "reportType": "users", "query": {"lob": "Marketing", "types": ["security"], "flattenMembership": false}, "searchQuery": "", "createdAt": "2025-07-22T17:04:24+02:00", "updatedAt": "2025-07-22T17:04:24+02:00", "isActive": false, "version": 2, "parentId": "preset_1753196664278432100", "sharedId": "shared_1753196664278432100", "schedule": {"enabled": false, "frequency": "", "dayOfWeek": 0, "dayOfMonth": 0, "hour": 0, "minute": 0, "intervalHours": 0, "intervalMinutes": 0, "nextRun": ""}, "repositoryId": ""}, {"id": "preset_1753196664413315500", "name": "Marketing Groups", "description": "", "reportType": "groups", "query": {"lob": "Marketing", "flattenMembership": false}, "searchQuery": "", "createdAt": "2025-07-22T17:04:24+02:00", "updatedAt": "2025-07-22T17:04:24+02:00", "isActive": true, "version": 1, "parentId": "preset_1753196664413315500", "sharedId": "shared_1753196664413315500", "schedule": {"enabled": false, "frequency": "", "dayOfWeek": 0, "dayOfMonth": 0, "hour": 0, "minute": 0, "intervalHours": 0, "intervalMinutes": 0, "nextRun": ""}, "repositoryId": ""}, {"id": "preset_1753196664454940800", "name": "Toggle Test Preset", "description": "Test preset for toggling activation", "reportType": "users", "query": {"lob": "Marketing", "flattenMembership": false}, "searchQuery": "", "createdAt": "2025-07-22T17:04:24+02:00", "updatedAt": "2025-07-22T17:04:24+02:00", "isActive": true, "version": 1, "parentId": "preset_1753196664454940800", "sharedId": "shared_1753196664454940800", "schedule": {"enabled": false, "frequency": "", "dayOfWeek": 0, "dayOfMonth": 0, "hour": 0, "minute": 0, "intervalHours": 0, "intervalMinutes": 0, "nextRun": ""}, "repositoryId": ""}]