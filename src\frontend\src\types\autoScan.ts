// Auto-scan configuration types for usage tracking

export interface TimeWindow {
  startHour: number; // 0-23
  endHour: number;   // 0-23
  timezone: string;  // e.g., "UTC", "America/New_York"
}

export interface LoadBalancingConfig {
  spreadAcrossDay: boolean;     // Spread scans across the day
  minIntervalBetween: number;   // Minimum time between scans (in minutes)
  maxScansPerHour: number;      // Maximum scans per hour
}

export interface NotificationConfig {
  onCompletion: boolean; // Notify when scan completes
  onFailure: boolean;    // Notify when scan fails
  onSummary: boolean;    // Send daily/weekly summary
}

export interface AutoScanConfig {
  id: string;
  repoId: string;
  enabled: boolean;
  frequency: 'daily' | 'weekly' | 'monthly';
  timeWindow: TimeWindow;
  maxConcurrentScans: number;
  scanAllGroups: boolean;
  targetGroups: string[];
  loadBalancing: LoadBalancingConfig;
  notificationConfig: NotificationConfig;
  createdAt: string;
  updatedAt: string;
  lastScanTime?: string;
  nextScanTime?: string;
}

export interface AutoScanStatus {
  configId: string;
  repoId: string;
  isActive: boolean;
  lastScanTime?: string;
  nextScanTime?: string;
  totalScansRun: number;
  successfulScans: number;
  failedScans: number;
  averageScanDuration: number; // in milliseconds
  currentlyScanning: string[]; // Groups currently being scanned
}

export interface AutoScanJob {
  id: string;
  configId: string;
  repoId: string;
  groupName: string;
  scheduledFor: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
  createdAt: string;
  startedAt?: string;
  completedAt?: string;
  error?: string;
  resultsCount: number;
}

export interface AutoScanSummary {
  repoId: string;
  period: 'daily' | 'weekly' | 'monthly';
  startTime: string;
  endTime: string;
  totalJobs: number;
  completedJobs: number;
  failedJobs: number;
  totalResults: number;
  averageDuration: number; // in milliseconds
  groupsScanned: string[];
  topFailureReasons: FailureReason[];
}

export interface FailureReason {
  reason: string;
  count: number;
}

// API Response types
export interface AutoScanConfigResponse {
  config: AutoScanConfig;
  status: AutoScanStatus;
}

export interface AutoScanJobsResponse {
  jobs: AutoScanJob[];
  total: number;
  page: number;
  pageSize: number;
}

export interface AutoScanSummaryResponse {
  summary: AutoScanSummary;
}

// Default configurations
export const DEFAULT_AUTO_SCAN_CONFIG: Partial<AutoScanConfig> = {
  enabled: false,
  frequency: 'daily',
  timeWindow: {
    startHour: 2,  // 2 AM
    endHour: 6,    // 6 AM
    timezone: 'UTC'
  },
  maxConcurrentScans: 3,
  scanAllGroups: true,
  targetGroups: [],
  loadBalancing: {
    spreadAcrossDay: true,
    minIntervalBetween: 5, // 5 minutes
    maxScansPerHour: 10
  },
  notificationConfig: {
    onCompletion: false,
    onFailure: true,
    onSummary: true
  }
};

// Utility functions
export const formatDuration = (milliseconds: number): string => {
  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
};

export const formatTimeWindow = (timeWindow: TimeWindow): string => {
  const formatHour = (hour: number) => {
    const period = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
    return `${displayHour}:00 ${period}`;
  };
  
  return `${formatHour(timeWindow.startHour)} - ${formatHour(timeWindow.endHour)} ${timeWindow.timezone}`;
};

export const getNextScanTime = (config: AutoScanConfig): Date => {
  if (config.nextScanTime) {
    return new Date(config.nextScanTime);
  }
  
  const now = new Date();
  switch (config.frequency) {
    case 'daily':
      return new Date(now.getTime() + 24 * 60 * 60 * 1000);
    case 'weekly':
      return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
    case 'monthly':
      const nextMonth = new Date(now);
      nextMonth.setMonth(nextMonth.getMonth() + 1);
      return nextMonth;
    default:
      return new Date(now.getTime() + 24 * 60 * 60 * 1000);
  }
};

export const isInTimeWindow = (config: AutoScanConfig, time: Date = new Date()): boolean => {
  // Convert to the configured timezone if specified
  let checkTime = time;
  if (config.timeWindow.timezone && config.timeWindow.timezone !== 'UTC') {
    // This is a simplified check - in a real implementation, you'd use a proper timezone library
    checkTime = new Date(time.toLocaleString("en-US", {timeZone: config.timeWindow.timezone}));
  }
  
  const hour = checkTime.getHours();
  const { startHour, endHour } = config.timeWindow;
  
  if (startHour <= endHour) {
    return hour >= startHour && hour <= endHour;
  } else {
    // Handles overnight windows like 22:00 to 06:00
    return hour >= startHour || hour <= endHour;
  }
};

export const getStatusColor = (status: AutoScanJob['status']): string => {
  switch (status) {
    case 'pending':
      return 'text-yellow-600 bg-yellow-50';
    case 'running':
      return 'text-blue-600 bg-blue-50';
    case 'completed':
      return 'text-green-600 bg-green-50';
    case 'failed':
      return 'text-red-600 bg-red-50';
    case 'skipped':
      return 'text-gray-600 bg-gray-50';
    default:
      return 'text-gray-600 bg-gray-50';
  }
};

export const getStatusIcon = (status: AutoScanJob['status']): string => {
  switch (status) {
    case 'pending':
      return '⏳';
    case 'running':
      return '🔄';
    case 'completed':
      return '✅';
    case 'failed':
      return '❌';
    case 'skipped':
      return '⏭️';
    default:
      return '❓';
  }
};

// Validation functions
export const validateAutoScanConfig = (config: Partial<AutoScanConfig>): string[] => {
  const errors: string[] = [];
  
  if (!config.repoId) {
    errors.push('Repository ID is required');
  }
  
  if (!config.frequency || !['daily', 'weekly', 'monthly'].includes(config.frequency)) {
    errors.push('Frequency must be daily, weekly, or monthly');
  }
  
  if (config.timeWindow) {
    if (config.timeWindow.startHour < 0 || config.timeWindow.startHour > 23) {
      errors.push('Start hour must be between 0 and 23');
    }
    if (config.timeWindow.endHour < 0 || config.timeWindow.endHour > 23) {
      errors.push('End hour must be between 0 and 23');
    }
  }
  
  if (config.maxConcurrentScans && config.maxConcurrentScans < 1) {
    errors.push('Max concurrent scans must be at least 1');
  }
  
  if (!config.scanAllGroups && (!config.targetGroups || config.targetGroups.length === 0)) {
    errors.push('Target groups must be specified when not scanning all groups');
  }
  
  return errors;
};
