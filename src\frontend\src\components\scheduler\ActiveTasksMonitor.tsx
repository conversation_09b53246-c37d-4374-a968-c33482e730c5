import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { CompactProgress } from '@/components/ui/enhanced-progress';
import { useAdminWebSocket } from '@/hooks/useAdminWebSocket';
import { UnifiedActiveTaskProgress, UnifiedTaskData } from '@/components/common/UnifiedActiveTaskProgress';
import { WebSocketConnectionStatus } from '@/components/common/ConnectionStatusIndicator';
import {
  ActiveTaskLoadingSkeleton,
  EmptyActiveTasksState,
  MonitorCardLoadingSkeleton
} from '@/components/common/StandardizedLoadingStates';
import {
  Loader,
  Clock,
  User,
  Database,
  Zap,
  Play,
  Pause,
  X,
  Activity,
  Eye,
  EyeOff,
  RefreshCw,
  AlertCircle
} from 'lucide-react';

import type { ActiveTask } from '@/types/scheduler';
import { Progress as ProgressType, RealTimeProgressUpdate } from '@/types/scanLogs';
import {
  TaskTypeColors,
  formatRelativeTime,
  getTaskTypeDisplayName,
  getProgressPercentage
} from '@/types/scheduler';

interface ActiveTasksMonitorProps {
  tasks: ActiveTask[];
  loading?: boolean;
  onRefresh?: () => void;
  onPauseTask?: (taskId: string) => void;
  onResumeTask?: (taskId: string) => void;
  onCancelTask?: (taskId: string) => void;
  enableRealTimeMonitoring?: boolean;
}

const ActiveTasksMonitor: React.FC<ActiveTasksMonitorProps> = ({
  tasks,
  loading = false,
  onRefresh,
  onPauseTask,
  onResumeTask,
  onCancelTask,
  enableRealTimeMonitoring = true
}) => {
  const [isRealTimeEnabled, setIsRealTimeEnabled] = useState(enableRealTimeMonitoring);
  const [realTimeProgress, setRealTimeProgress] = useState<Map<string, ProgressType>>(new Map());
  const [globalScans, setGlobalScans] = useState<Map<string, any>>(new Map());

  // Admin WebSocket for real-time progress monitoring
  const {
    isConnected,
    isConnecting,
    error: wsError,
    lastUpdate,
    isAdminSubscribed,
    subscribeToAdminMonitoring,
    unsubscribeFromAdminMonitoring,
    connect,
    getActiveScans
  } = useAdminWebSocket({
    autoConnect: isRealTimeEnabled
  });

  // Subscribe to admin monitoring when connected and real-time is enabled
  useEffect(() => {
    console.log(`ActiveTasksMonitor: Connection state - isConnected: ${isConnected}, isRealTimeEnabled: ${isRealTimeEnabled}, isAdminSubscribed: ${isAdminSubscribed}`);

    if (isConnected && isRealTimeEnabled && !isAdminSubscribed) {
      console.log('ActiveTasksMonitor: Subscribing to admin monitoring');
      subscribeToAdminMonitoring();
    } else if (isAdminSubscribed && !isRealTimeEnabled) {
      console.log('ActiveTasksMonitor: Unsubscribing from admin monitoring');
      unsubscribeFromAdminMonitoring();
    }
  }, [isConnected, isRealTimeEnabled, isAdminSubscribed, subscribeToAdminMonitoring, unsubscribeFromAdminMonitoring]);

  // Subscribe to admin monitoring when enabled
  useEffect(() => {
    if (isConnected && isRealTimeEnabled && !isAdminSubscribed) {
      subscribeToAdminMonitoring();
    } else if (isAdminSubscribed && !isRealTimeEnabled) {
      unsubscribeFromAdminMonitoring();
    }
  }, [isConnected, isRealTimeEnabled, isAdminSubscribed, subscribeToAdminMonitoring, unsubscribeFromAdminMonitoring]);

  // Handle real-time progress updates and global scan tracking
  useEffect(() => {
    if (!lastUpdate || !isRealTimeEnabled) return;

    const update = lastUpdate;

    // Track all active scans globally (for comprehensive monitoring)
    if (update.eventType === 'scan_start') {
      setGlobalScans(prev => {
        const newScans = new Map(prev);
        const scanKey = `${update.repoId}-${update.groupName}`;
        newScans.set(scanKey, {
          scanId: update.scanId,
          repoId: update.repoId,
          groupName: update.groupName,
          startTime: Date.now(),
          status: 'running',
          progress: update.progress
        });
        return newScans;
      });
    }

    // Map scan updates to task progress
    if (update.progress && (update.eventType === 'progress' || update.eventType === 'file_progress' || update.eventType === 'chunk_start' || update.eventType === 'chunk_complete')) {
      setRealTimeProgress(prev => {
        const newProgress = new Map(prev);

        // Try to match the scan to an active task
        // For usage scans, the task ID is "GroupName:RepoID" and scanId is "GroupName:RepoID:active"
        const matchingTask = tasks?.find(task => {
          if (task.type === 'usage_scan') {
            // For usage scans, match by task ID (GroupName:RepoID) with scanId (GroupName:RepoID:active)
            const expectedScanId = `${task.id}:active`;
            return update.scanId === expectedScanId;
          } else if (task.type === 'scan' || task.type === 'auto-scan') {
            // For other scan types, match by repository and group name
            return task.repository === update.repoId && task.groupName === update.groupName;
          }
          return false;
        });

        if (matchingTask && update.progress) {
          console.log(`ActiveTasksMonitor: Updating progress for task ${matchingTask.id} (${matchingTask.type}): ${update.progress.percentage}%`);
          newProgress.set(matchingTask.id, update.progress);
        } else {
          console.log(`ActiveTasksMonitor: No matching task found for scan update: ${update.groupName} in ${update.repoId} (scanId: ${update.scanId})`);
          console.log(`ActiveTasksMonitor: Available tasks:`, tasks?.map(t => ({ id: t.id, type: t.type, repo: t.repository, group: t.groupName })));
        }

        return newProgress;
      });

      // Update global scan progress
      setGlobalScans(prev => {
        const newScans = new Map(prev);
        const scanKey = `${update.repoId}-${update.groupName}`;
        if (newScans.has(scanKey)) {
          newScans.set(scanKey, {
            ...newScans.get(scanKey),
            progress: update.progress,
            lastUpdate: Date.now()
          });
        }
        return newScans;
      });
    }

    // Clean up completed/failed scans
    if (update.eventType === 'scan_complete' || update.eventType === 'scan_error') {
      setRealTimeProgress(prev => {
        const newProgress = new Map(prev);
        const matchingTask = tasks?.find(task => {
          if (task.type === 'usage_scan') {
            // For usage scans, match by task ID (GroupName:RepoID) with scanId (GroupName:RepoID:active)
            const expectedScanId = `${task.id}:active`;
            return update.scanId === expectedScanId;
          } else if (task.type === 'scan' || task.type === 'auto-scan') {
            // For other scan types, match by repository and group name
            return task.repository === update.repoId && task.groupName === update.groupName;
          }
          return false;
        });

        if (matchingTask) {
          console.log(`ActiveTasksMonitor: Cleaning up progress for completed/failed scan: ${matchingTask.id} (${matchingTask.type})`);
          newProgress.delete(matchingTask.id);
        }

        return newProgress;
      });

      // Update global scan status
      setGlobalScans(prev => {
        const newScans = new Map(prev);
        const scanKey = `${update.repoId}-${update.groupName}`;
        if (newScans.has(scanKey)) {
          newScans.set(scanKey, {
            ...newScans.get(scanKey),
            status: update.eventType === 'scan_complete' ? 'completed' : 'failed',
            endTime: Date.now()
          });
          // Remove completed/failed scans after a delay
          setTimeout(() => {
            setGlobalScans(current => {
              const updated = new Map(current);
              updated.delete(scanKey);
              return updated;
            });
          }, 5000);
        }
        return newScans;
      });
    }
  }, [lastUpdate, isRealTimeEnabled, tasks]);

  const toggleRealTimeMonitoring = () => {
    setIsRealTimeEnabled(!isRealTimeEnabled);
    if (!isConnected && !isRealTimeEnabled) {
      connect();
    }
  };

  // Restore progress for active scans when component mounts or real-time is enabled
  useEffect(() => {
    if (isRealTimeEnabled && isConnected && isAdminSubscribed && tasks && tasks.length > 0) {
      console.log('ActiveTasksMonitor: Checking for active scan tasks to restore progress');

      // Check for active scan tasks and try to restore their progress
      tasks.forEach(task => {
        if (task.status === 'running' && (task.type === 'scan' || task.type === 'usage_scan' || task.type === 'auto-scan') && task.repository && task.groupName) {
          let scanKey: string;
          let expectedScanId: string;

          if (task.type === 'usage_scan') {
            // For usage scans, the scanKey and scanId are based on task ID
            scanKey = `${task.repository}-${task.groupName}`;
            expectedScanId = `${task.id}:active`; // task.id is already "GroupName:RepoID"
          } else {
            // For other scan types
            scanKey = `${task.repository}-${task.groupName}`;
            expectedScanId = `${task.groupName}:${task.repository}`;
          }

          console.log(`ActiveTasksMonitor: Found active ${task.type} task: ${task.name} (${task.groupName} in ${task.repository})`);
          console.log(`ActiveTasksMonitor: Expected scanId: ${expectedScanId}`);

          // Check if we already have progress for this task
          if (!realTimeProgress.has(task.id)) {
            console.log(`ActiveTasksMonitor: Attempting to restore progress for: ${task.groupName} in ${task.repository}`);

            // Try to find existing global scan data
            const existingGlobalScan = globalScans.get(scanKey);
            if (existingGlobalScan && existingGlobalScan.progress) {
              console.log(`ActiveTasksMonitor: Restoring progress from global scan data for: ${task.id}`);
              // Restore progress from global scan data
              setRealTimeProgress(prev => {
                const newProgress = new Map(prev);
                newProgress.set(task.id, existingGlobalScan.progress);
                return newProgress;
              });
            } else {
              console.log(`ActiveTasksMonitor: No existing global scan data found for: ${scanKey}`);

              // Create a placeholder progress entry to indicate we're tracking this scan
              setRealTimeProgress(prev => {
                const newProgress = new Map(prev);
                newProgress.set(task.id, {
                  current: 0,
                  total: 100,
                  percentage: 0,
                  description: 'Connecting to scan progress...',
                  currentFile: undefined
                });
                return newProgress;
              });
            }
          }
        }
      });
    }
  }, [isRealTimeEnabled, isConnected, isAdminSubscribed, tasks]);
  if (loading) {
    return <MonitorCardLoadingSkeleton />;
  }

  // Get active scans from WebSocket that don't have corresponding active tasks
  const activeWebSocketScans = Array.from(globalScans.values()).filter(scan => {
    if (scan.status !== 'running') return false;

    // Check if there's already an active task for this scan
    const hasActiveTask = tasks?.some(task =>
      task.type === 'usage_scan' &&
      task.repository === scan.repoId &&
      task.groupName === scan.groupName &&
      task.status === 'running'
    );

    // Only include scans that don't have corresponding active tasks
    return !hasActiveTask;
  });

  const totalActiveItems = (tasks?.length || 0) + activeWebSocketScans.length;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Zap className="h-5 w-5 text-green-600" />
              <span>Active Tasks & Real-time Scans</span>
              <Badge variant="secondary">{totalActiveItems} active</Badge>
            </CardTitle>
            <CardDescription>
              Scheduler tasks and real-time scan monitoring in one unified view
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            {/* Real-time monitoring with integrated connection status */}
            <WebSocketConnectionStatus
              isConnected={isConnected}
              isConnecting={isConnecting}
              error={wsError}
              onConnect={() => {
                setIsRealTimeEnabled(true);
                connect();
              }}
              onDisconnect={() => setIsRealTimeEnabled(false)}
            />

            {/* Refresh button */}
            {onRefresh && (
              <Button
                variant="outline"
                size="sm"
                onClick={onRefresh}
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                {loading ? 'Refreshing...' : 'Refresh'}
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {totalActiveItems === 0 ? (
          <EmptyActiveTasksState
            title="No Active Tasks or Scans"
            description="All tasks are currently idle. New tasks and scans will appear here when they start running."
            icon={Clock}
          />
        ) : (
          <div className="space-y-4">
            {/* Scheduler Tasks */}
            {(tasks || []).map((task) => (
              <div
                key={task.id}
                className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
              >
                {/* Task Header */}
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <Badge className={`${TaskTypeColors[task.type]} border-0`}>
                      {getTaskTypeDisplayName(task.type)}
                    </Badge>
                    <Badge
                      variant="outline"
                      className={`${task.status === 'running' ? 'border-green-500 text-green-700' : 'border-yellow-500 text-yellow-700'}`}
                    >
                      <Loader className="h-3 w-3 mr-1 animate-spin" />
                      {task.status}
                    </Badge>
                    <h3 className="font-medium">{task.name}</h3>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-500">
                      Started {formatRelativeTime(task.startedAt)}
                    </span>
                    <div className="flex items-center space-x-1">
                      {onPauseTask && task.status === 'running' && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onPauseTask(task.id)}
                          title="Pause Task"
                        >
                          <Pause className="h-4 w-4" />
                        </Button>
                      )}
                      {onResumeTask && task.status !== 'running' && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onResumeTask(task.id)}
                          title="Resume Task"
                        >
                          <Play className="h-4 w-4" />
                        </Button>
                      )}
                      {onCancelTask && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onCancelTask(task.id)}
                          title="Cancel Task"
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                </div>

                {/* Progress Bar */}
                {(task.progress || realTimeProgress.has(task.id)) && (
                  <div className="mb-3">
                    {/* Use real-time progress if available, otherwise fall back to task progress */}
                    {realTimeProgress.has(task.id) && isRealTimeEnabled ? (
                      <div className="space-y-2">
                        <div className="flex items-center gap-2 text-xs text-muted-foreground">
                          <Activity className="h-3 w-3 text-green-500" />
                          <span>Live Progress</span>
                        </div>
                        <CompactProgress
                          progress={realTimeProgress.get(task.id)!}
                          className="mt-1"
                          showCurrentFile={true}
                        />
                      </div>
                    ) : task.progress ? (
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-gray-700">
                            {task.progress.description || 'Processing...'}
                          </span>
                          <span className="text-sm text-gray-500">
                            {task.progress.current} / {task.progress.total} ({getProgressPercentage(task.progress)}%)
                          </span>
                        </div>
                        <Progress
                          value={getProgressPercentage(task.progress)}
                          className="h-2"
                        />
                      </div>
                    ) : null}
                  </div>
                )}

                {/* Task Details */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                  <div className="flex items-center space-x-2">
                    <Database className="h-4 w-4" />
                    <div>
                      <span className="font-medium">Repository:</span>
                      <div>{task.repository || 'N/A'}</div>
                    </div>
                  </div>

                  {task.groupName && (
                    <div className="flex items-center space-x-2">
                      <User className="h-4 w-4" />
                      <div>
                        <span className="font-medium">Group:</span>
                        <div>{task.groupName}</div>
                      </div>
                    </div>
                  )}

                  {task.workerId && (
                    <div className="flex items-center space-x-2">
                      <Zap className="h-4 w-4" />
                      <div>
                        <span className="font-medium">Worker:</span>
                        <div>{task.workerId}</div>
                      </div>
                    </div>
                  )}

                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4" />
                    <div>
                      <span className="font-medium">Started:</span>
                      <div>{new Date(task.startedAt).toLocaleTimeString()}</div>
                    </div>
                  </div>
                </div>

                {/* Metadata */}
                {task.metadata && Object.keys(task.metadata).length > 0 && (
                  <div className="mt-3 pt-3 border-t">
                    <details className="group">
                      <summary className="cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900">
                        Task Metadata ({Object.keys(task.metadata).length} items)
                      </summary>
                      <div className="mt-2 pl-4 space-y-1">
                        {Object.entries(task.metadata).map(([key, value]) => (
                          <div key={key} className="text-xs text-gray-600">
                            <span className="font-medium">{key}:</span> {JSON.stringify(value)}
                          </div>
                        ))}
                      </div>
                    </details>
                  </div>
                )}
              </div>
            ))}

            {/* Real-time Scans (not tracked by scheduler) */}
            {activeWebSocketScans.length > 0 && (
              <>
                {(tasks?.length || 0) > 0 && (
                  <div className="border-t pt-4 mt-4">
                    <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center gap-2">
                      <Activity className="h-4 w-4 text-blue-500" />
                      Real-time Scans ({activeWebSocketScans.length})
                    </h4>
                  </div>
                )}
                {activeWebSocketScans.map((scan) => (
                  <div
                    key={`${scan.repoId}-${scan.groupName}`}
                    className="border rounded-lg p-4 hover:bg-blue-50/50 transition-colors border-blue-200"
                  >
                    {/* Scan Header */}
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <Badge className="bg-blue-100 text-blue-800 border-blue-200">
                          Live Scan
                        </Badge>
                        <Badge variant="outline" className="border-green-500 text-green-700">
                          <Loader className="h-3 w-3 mr-1 animate-spin" />
                          running
                        </Badge>
                        <h3 className="font-medium">Usage Scan: {scan.groupName}</h3>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-500">
                          Started {Math.floor((Date.now() - scan.startTime) / 60000)} minutes ago
                        </span>
                      </div>
                    </div>

                    {/* Live Progress */}
                    {scan.progress && (
                      <div className="mb-3">
                        <div className="space-y-2">
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <Activity className="h-3 w-3 text-green-500 animate-pulse" />
                            <span>Live Progress</span>
                          </div>
                          <CompactProgress
                            progress={scan.progress}
                            className="mt-1"
                            showCurrentFile={true}
                          />
                        </div>
                      </div>
                    )}

                    {/* Scan Details */}
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm text-gray-600">
                      <div className="flex items-center space-x-2">
                        <Database className="h-4 w-4" />
                        <div>
                          <span className="font-medium">Repository:</span>
                          <div>{scan.repoId}</div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <User className="h-4 w-4" />
                        <div>
                          <span className="font-medium">Group:</span>
                          <div>{scan.groupName}</div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4" />
                        <div>
                          <span className="font-medium">Scan ID:</span>
                          <div className="font-mono text-xs">{scan.scanId?.substring(0, 8)}...</div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ActiveTasksMonitor;
