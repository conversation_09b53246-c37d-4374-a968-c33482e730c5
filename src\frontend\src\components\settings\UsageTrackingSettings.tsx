import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Settings,
  Clock,
  Users,
  FileText,
  Search,
  AlertCircle,
  CheckCircle,
  RefreshCw,
  ExternalLink,
  Plus,
  Edit,
  Trash2,
  Activity
} from 'lucide-react';

import UsageSourcesSettings from '@/components/settings/UsageSourcesSettings';
import { useRepository } from '@/context/RepositoryContext';
import { useToast } from '@/components/ui/use-toast';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { apiClient } from '@/api/client';
import type {
  AutoScanConfig,
  AutoScanStatus,
  TimeWindow,
  LoadBalancingConfig,
  NotificationConfig
} from '@/types/autoScan';
import { DEFAULT_AUTO_SCAN_CONFIG } from '@/types/autoScan';

interface UsageTrackingSettingsProps {
  className?: string;
}

const UsageTrackingSettings: React.FC<UsageTrackingSettingsProps> = ({ className }) => {
  const { repositories, selectedRepoId, isLoading: repoLoading } = useRepository();
  const { toast } = useToast();
  const [selectedRepo, setSelectedRepo] = useState<string>('');
  const [autoScanConfig, setAutoScanConfig] = useState<AutoScanConfig | null>(null);
  const [autoScanStatus, setAutoScanStatus] = useState<AutoScanStatus | null>(null);
  const [configLoading, setConfigLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [globalSettings, setGlobalSettings] = useState({
    maxConcurrentScans: 5,
    defaultScanFrequency: 'daily',
    enableNotifications: true,
    retentionDays: 30
  });

  // Initialize with current repository or first available repository
  useEffect(() => {
    if (repositories.length > 0) {
      // Use the currently selected repository from context, or fall back to first repository
      const repoToSelect = selectedRepoId && repositories.find(r => r.id === selectedRepoId)
        ? selectedRepoId
        : repositories[0].id;
      setSelectedRepo(repoToSelect);
    }
  }, [repositories, selectedRepoId]);

  // Load configuration when selected repository changes
  useEffect(() => {
    if (selectedRepo) {
      loadAutoScanConfig(selectedRepo);
    }
  }, [selectedRepo]);

  // Load global settings on component mount
  useEffect(() => {
    loadGlobalSettings();
  }, []);

  const loadGlobalSettings = async () => {
    try {
      // TODO: Replace with actual API call when backend is ready
      // const settings = await apiClient.settings.getGlobalUsageSettings();
      setGlobalSettings({
        maxConcurrentScans: 5,
        defaultScanFrequency: 'daily',
        enableNotifications: true,
        retentionDays: 30
      });
    } catch (error) {
      console.error('Failed to load global settings:', error);
    }
  };

  const loadAutoScanConfig = async (repoId: string) => {
    if (!repoId) return;

    setConfigLoading(true);
    setAutoScanConfig(null);
    setAutoScanStatus(null);

    try {
      // TODO: Replace with actual API call when backend is ready
      // const response = await apiClient.autoScan.getConfig(repoId);

      // Create repository-specific mock data to demonstrate the functionality
      const repoSpecificData = getRepositorySpecificData(repoId);

      const defaultConfig: AutoScanConfig = {
        id: `config-${repoId}`,
        repoId,
        enabled: repoSpecificData.enabled,
        frequency: repoSpecificData.frequency,
        timeWindow: {
          startHour: repoSpecificData.startHour,
          endHour: repoSpecificData.endHour,
          timezone: repoSpecificData.timezone
        },
        maxConcurrentScans: repoSpecificData.maxConcurrentScans,
        scanAllGroups: repoSpecificData.scanAllGroups,
        targetGroups: repoSpecificData.targetGroups,
        loadBalancing: {
          spreadAcrossDay: repoSpecificData.spreadAcrossDay,
          minIntervalBetween: repoSpecificData.minIntervalBetween,
          maxScansPerHour: repoSpecificData.maxScansPerHour
        },
        notificationConfig: {
          onCompletion: repoSpecificData.onCompletion,
          onFailure: repoSpecificData.onFailure,
          onSummary: repoSpecificData.onSummary
        },
        createdAt: repoSpecificData.createdAt,
        updatedAt: repoSpecificData.updatedAt,
        lastScanTime: repoSpecificData.lastScanTime,
        nextScanTime: repoSpecificData.nextScanTime
      };

      const defaultStatus: AutoScanStatus = {
        configId: defaultConfig.id,
        repoId,
        isActive: repoSpecificData.isActive,
        totalScansRun: repoSpecificData.totalScansRun,
        successfulScans: repoSpecificData.successfulScans,
        failedScans: repoSpecificData.failedScans,
        averageScanDuration: repoSpecificData.averageScanDuration,
        currentlyScanning: repoSpecificData.currentlyScanning,
        lastScanTime: repoSpecificData.lastScanTime,
        nextScanTime: repoSpecificData.nextScanTime
      };

      // Simulate loading delay to show the loading state
      await new Promise(resolve => setTimeout(resolve, 500));

      setAutoScanConfig(defaultConfig);
      setAutoScanStatus(defaultStatus);
    } catch (error) {
      console.error('Failed to load auto-scan config:', error);
      toast({
        title: 'Failed to load configuration',
        description: 'Could not load auto-scan configuration for this repository.',
        variant: 'destructive',
      });
    } finally {
      setConfigLoading(false);
    }
  };

  // Helper function to generate repository-specific data
  const getRepositorySpecificData = (repoId: string) => {
    const repo = repositories.find(r => r.id === repoId);
    const repoName = repo?.name || 'Unknown';

    // Create different configurations based on repository
    const configs: Record<string, any> = {
      // Default configuration for unknown repositories
      default: {
        enabled: false,
        frequency: 'daily',
        startHour: 2,
        endHour: 6,
        timezone: 'UTC',
        maxConcurrentScans: 3,
        scanAllGroups: true,
        targetGroups: [],
        spreadAcrossDay: true,
        minIntervalBetween: 5,
        maxScansPerHour: 10,
        onCompletion: false,
        onFailure: true,
        onSummary: true,
        isActive: false,
        totalScansRun: 0,
        successfulScans: 0,
        failedScans: 0,
        averageScanDuration: 0,
        currentlyScanning: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        lastScanTime: undefined,
        nextScanTime: undefined
      }
    };

    // Repository-specific configurations
    if (repoName.toLowerCase().includes('adgitops') || repoName.toLowerCase().includes('main')) {
      return {
        ...configs.default,
        enabled: true,
        frequency: 'daily',
        startHour: 1,
        endHour: 5,
        timezone: 'UTC',
        maxConcurrentScans: 5,
        isActive: true,
        totalScansRun: 45,
        successfulScans: 42,
        failedScans: 3,
        averageScanDuration: 120000,
        currentlyScanning: ['FM-DevOps'],
        lastScanTime: new Date(Date.now() - 3600000).toISOString(),
        nextScanTime: new Date(Date.now() + 82800000).toISOString(), // 23 hours from now
        createdAt: new Date(Date.now() - 86400000 * 30).toISOString(), // 30 days ago
        updatedAt: new Date(Date.now() - 3600000).toISOString() // 1 hour ago
      };
    } else if (repoName.toLowerCase().includes('config')) {
      return {
        ...configs.default,
        enabled: true,
        frequency: 'weekly',
        startHour: 3,
        endHour: 7,
        timezone: 'America/New_York',
        maxConcurrentScans: 2,
        isActive: true,
        totalScansRun: 12,
        successfulScans: 11,
        failedScans: 1,
        averageScanDuration: 90000,
        currentlyScanning: [],
        lastScanTime: new Date(Date.now() - 86400000 * 2).toISOString(), // 2 days ago
        nextScanTime: new Date(Date.now() + 86400000 * 5).toISOString(), // 5 days from now
        createdAt: new Date(Date.now() - 86400000 * 60).toISOString(), // 60 days ago
        updatedAt: new Date(Date.now() - 86400000).toISOString() // 1 day ago
      };
    } else if (repoName.toLowerCase().includes('archive')) {
      return {
        ...configs.default,
        enabled: false,
        frequency: 'monthly',
        startHour: 4,
        endHour: 8,
        timezone: 'Europe/London',
        maxConcurrentScans: 1,
        isActive: false,
        totalScansRun: 3,
        successfulScans: 3,
        failedScans: 0,
        averageScanDuration: 60000,
        currentlyScanning: [],
        lastScanTime: new Date(Date.now() - 86400000 * 30).toISOString(), // 30 days ago
        nextScanTime: undefined,
        createdAt: new Date(Date.now() - 86400000 * 90).toISOString(), // 90 days ago
        updatedAt: new Date(Date.now() - 86400000 * 30).toISOString() // 30 days ago
      };
    }

    return configs.default;
  };

  const handleSaveGlobalSettings = async () => {
    setSaving(true);
    try {
      // TODO: Replace with actual API call when backend is ready
      // await apiClient.settings.saveGlobalUsageSettings(globalSettings);
      console.log('Saving global settings:', globalSettings);

      toast({
        title: 'Settings Saved',
        description: 'Global usage tracking settings have been saved successfully.',
      });
    } catch (error) {
      console.error('Failed to save global settings:', error);
      toast({
        title: 'Save Failed',
        description: 'Failed to save global settings. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  const handleSaveAutoScanConfig = async () => {
    if (!autoScanConfig) return;

    setSaving(true);
    try {
      // TODO: Replace with actual API call when backend is ready
      // await apiClient.autoScan.saveConfig(autoScanConfig);
      console.log('Saving auto-scan config:', autoScanConfig);

      // Update the config with new timestamp
      const updatedConfig = {
        ...autoScanConfig,
        updatedAt: new Date().toISOString()
      };
      setAutoScanConfig(updatedConfig);

      toast({
        title: 'Configuration Saved',
        description: 'Auto-scan configuration has been saved successfully.',
      });
    } catch (error) {
      console.error('Failed to save auto-scan config:', error);
      toast({
        title: 'Save Failed',
        description: 'Failed to save auto-scan configuration. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  const updateAutoScanConfig = (updates: Partial<AutoScanConfig>) => {
    if (!autoScanConfig) return;
    setAutoScanConfig({ ...autoScanConfig, ...updates });
  };

  const updateTimeWindow = (updates: Partial<TimeWindow>) => {
    if (!autoScanConfig) return;
    updateAutoScanConfig({
      timeWindow: { ...autoScanConfig.timeWindow, ...updates }
    });
  };

  const updateLoadBalancing = (updates: Partial<LoadBalancingConfig>) => {
    if (!autoScanConfig) return;
    updateAutoScanConfig({
      loadBalancing: { ...autoScanConfig.loadBalancing, ...updates }
    });
  };

  const updateNotifications = (updates: Partial<NotificationConfig>) => {
    if (!autoScanConfig) return;
    updateAutoScanConfig({
      notificationConfig: { ...autoScanConfig.notificationConfig, ...updates }
    });
  };



  return (
    <div className={`space-y-6 ${className}`}>
      {/* Global Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Settings className="h-5 w-5 mr-2" />
            Global Usage Tracking Settings
          </CardTitle>
          <CardDescription>
            Configure global settings that apply to all repositories and usage tracking operations.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="maxConcurrentScans">Max Concurrent Scans</Label>
              <Input
                id="maxConcurrentScans"
                type="number"
                min="1"
                max="20"
                value={globalSettings.maxConcurrentScans}
                onChange={(e) => setGlobalSettings(prev => ({
                  ...prev,
                  maxConcurrentScans: parseInt(e.target.value) || 5
                }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="defaultFrequency">Default Scan Frequency</Label>
              <Select
                value={globalSettings.defaultScanFrequency}
                onValueChange={(value) => setGlobalSettings(prev => ({
                  ...prev,
                  defaultScanFrequency: value
                }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="weekly">Weekly</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="retentionDays">Log Retention (Days)</Label>
              <Input
                id="retentionDays"
                type="number"
                min="1"
                max="365"
                value={globalSettings.retentionDays}
                onChange={(e) => setGlobalSettings(prev => ({
                  ...prev,
                  retentionDays: parseInt(e.target.value) || 30
                }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="enableNotifications">Enable Notifications</Label>
              <div className="flex items-center space-x-2">
                <Switch
                  id="enableNotifications"
                  checked={globalSettings.enableNotifications}
                  onCheckedChange={(checked) => setGlobalSettings(prev => ({
                    ...prev,
                    enableNotifications: checked
                  }))}
                />
                <span className="text-sm text-muted-foreground">
                  {globalSettings.enableNotifications ? 'Enabled' : 'Disabled'}
                </span>
              </div>
            </div>
          </div>

          <div className="flex justify-end">
            <Button onClick={handleSaveGlobalSettings} disabled={saving}>
              {saving ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save Global Settings'
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Repository Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Activity className="h-5 w-5 mr-2" />
            Repository-Specific Configuration
          </CardTitle>
          <CardDescription>
            Configure automated scanning and usage tracking for your configured repositories.
            These are the repositories you've set up in the Repository Integration tab that contain your group definitions and access control files.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="repository">Select Repository</Label>
            <Select value={selectedRepo} onValueChange={setSelectedRepo} disabled={repoLoading}>
              <SelectTrigger>
                <SelectValue placeholder={repoLoading ? "Loading repositories..." : "Select a repository"} />
              </SelectTrigger>
              <SelectContent>
                {repositories.map((repo) => (
                  <SelectItem key={repo.id} value={repo.id}>
                    <div className="flex items-center space-x-2">
                      <span>{repo.name}</span>
                      <Badge variant={repo.isActive ? "default" : "secondary"} className="text-xs">
                        {repo.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {repo.type.toUpperCase()}
                      </Badge>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {repositories.length === 0 && !repoLoading && (
              <p className="text-sm text-muted-foreground">
                No repositories configured. Please configure a repository in the Repository Integration tab first.
              </p>
            )}
          </div>

          {selectedRepo && configLoading && (
            <div className="space-y-4 pt-4 border-t">
              <div className="flex items-center justify-center py-8">
                <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                <span>Loading configuration for {repositories.find(r => r.id === selectedRepo)?.name}...</span>
              </div>
            </div>
          )}

          {selectedRepo && !configLoading && autoScanStatus && (
            <div className="space-y-4 pt-4 border-t">
              {/* Scan Status Display */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-lg font-medium text-blue-900">Scanning Status</h3>
                  <Badge variant="outline" className="text-blue-700 border-blue-300">
                    {repositories.find(r => r.id === selectedRepo)?.name}
                  </Badge>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <div className="font-medium text-blue-700">Status</div>
                    <div className="flex items-center space-x-1">
                      {autoScanStatus.isActive ? (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : (
                        <AlertCircle className="h-4 w-4 text-yellow-500" />
                      )}
                      <span>{autoScanStatus.isActive ? 'Active' : 'Inactive'}</span>
                    </div>
                  </div>
                  <div>
                    <div className="font-medium text-blue-700">Total Scans</div>
                    <div>{autoScanStatus.totalScansRun}</div>
                  </div>
                  <div>
                    <div className="font-medium text-blue-700">Success Rate</div>
                    <div>
                      {autoScanStatus.totalScansRun > 0
                        ? Math.round((autoScanStatus.successfulScans / autoScanStatus.totalScansRun) * 100)
                        : 0}%
                    </div>
                  </div>
                  <div>
                    <div className="font-medium text-blue-700">Next Scan</div>
                    <div>
                      {autoScanStatus.nextScanTime
                        ? new Date(autoScanStatus.nextScanTime).toLocaleString()
                        : 'Not scheduled'}
                    </div>
                  </div>
                </div>
                {autoScanStatus.currentlyScanning.length > 0 && (
                  <div className="mt-3 pt-3 border-t border-blue-200">
                    <div className="font-medium text-blue-700 mb-1">Currently Scanning:</div>
                    <div className="flex flex-wrap gap-1">
                      {autoScanStatus.currentlyScanning.map((group) => (
                        <Badge key={group} variant="secondary" className="text-xs">
                          {group}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Inline Configuration */}
              {autoScanConfig && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <h3 className="text-lg font-medium">Auto-Scan Configuration</h3>
                      <Badge variant="outline" className="text-xs">
                        {repositories.find(r => r.id === selectedRepo)?.name}
                      </Badge>
                    </div>
                    <Button onClick={handleSaveAutoScanConfig} disabled={saving || configLoading}>
                      {saving ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        'Save Configuration'
                      )}
                    </Button>
                  </div>

                  <Tabs defaultValue="basic" className="w-full">
                    <TabsList className="grid w-full grid-cols-4">
                      <TabsTrigger value="basic">Basic</TabsTrigger>
                      <TabsTrigger value="schedule">Schedule</TabsTrigger>
                      <TabsTrigger value="advanced">Advanced</TabsTrigger>
                      <TabsTrigger value="notifications">Notifications</TabsTrigger>
                    </TabsList>

                    <TabsContent value="basic" className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label>Enable Automated Scanning</Label>
                          <div className="flex items-center space-x-2">
                            <Switch
                              checked={autoScanConfig.enabled}
                              onCheckedChange={(enabled) => updateAutoScanConfig({ enabled })}
                            />
                            <span className="text-sm text-muted-foreground">
                              {autoScanConfig.enabled ? 'Enabled' : 'Disabled'}
                            </span>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label>Scan Frequency</Label>
                          <Select
                            value={autoScanConfig.frequency}
                            onValueChange={(frequency: 'daily' | 'weekly' | 'monthly') =>
                              updateAutoScanConfig({ frequency })
                            }
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="daily">Daily</SelectItem>
                              <SelectItem value="weekly">Weekly</SelectItem>
                              <SelectItem value="monthly">Monthly</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label>Max Concurrent Scans</Label>
                          <Input
                            type="number"
                            min="1"
                            max="10"
                            value={autoScanConfig.maxConcurrentScans}
                            onChange={(e) => updateAutoScanConfig({
                              maxConcurrentScans: parseInt(e.target.value) || 3
                            })}
                          />
                        </div>

                        <div className="space-y-2">
                          <Label>Scan Target</Label>
                          <div className="flex items-center space-x-2">
                            <Switch
                              checked={autoScanConfig.scanAllGroups}
                              onCheckedChange={(scanAllGroups) => updateAutoScanConfig({ scanAllGroups })}
                            />
                            <span className="text-sm text-muted-foreground">
                              {autoScanConfig.scanAllGroups ? 'All Groups' : 'Specific Groups'}
                            </span>
                          </div>
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="schedule" className="space-y-4">
                      <div className="grid grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <Label>Start Hour (24h format)</Label>
                          <Input
                            type="number"
                            min="0"
                            max="23"
                            value={autoScanConfig.timeWindow.startHour}
                            onChange={(e) => updateTimeWindow({
                              startHour: parseInt(e.target.value) || 0
                            })}
                          />
                        </div>

                        <div className="space-y-2">
                          <Label>End Hour (24h format)</Label>
                          <Input
                            type="number"
                            min="0"
                            max="23"
                            value={autoScanConfig.timeWindow.endHour}
                            onChange={(e) => updateTimeWindow({
                              endHour: parseInt(e.target.value) || 6
                            })}
                          />
                        </div>

                        <div className="space-y-2">
                          <Label>Timezone</Label>
                          <Select
                            value={autoScanConfig.timeWindow.timezone}
                            onValueChange={(timezone) => updateTimeWindow({ timezone })}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="UTC">UTC</SelectItem>
                              <SelectItem value="America/New_York">Eastern Time</SelectItem>
                              <SelectItem value="America/Chicago">Central Time</SelectItem>
                              <SelectItem value="America/Denver">Mountain Time</SelectItem>
                              <SelectItem value="America/Los_Angeles">Pacific Time</SelectItem>
                              <SelectItem value="Europe/London">London</SelectItem>
                              <SelectItem value="Europe/Paris">Paris</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="advanced" className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label>Max Scans Per Hour</Label>
                          <Input
                            type="number"
                            min="1"
                            max="60"
                            value={autoScanConfig.loadBalancing.maxScansPerHour}
                            onChange={(e) => updateLoadBalancing({
                              maxScansPerHour: parseInt(e.target.value) || 10
                            })}
                          />
                        </div>

                        <div className="space-y-2">
                          <Label>Min Interval Between Scans (minutes)</Label>
                          <Input
                            type="number"
                            min="1"
                            max="60"
                            value={autoScanConfig.loadBalancing.minIntervalBetween}
                            onChange={(e) => updateLoadBalancing({
                              minIntervalBetween: parseInt(e.target.value) || 5
                            })}
                          />
                        </div>

                        <div className="space-y-2 col-span-2">
                          <div className="flex items-center space-x-2">
                            <Switch
                              checked={autoScanConfig.loadBalancing.spreadAcrossDay}
                              onCheckedChange={(spreadAcrossDay) => updateLoadBalancing({ spreadAcrossDay })}
                            />
                            <Label>Spread scans across the day</Label>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            When enabled, scans will be distributed throughout the allowed time window instead of running all at once.
                          </p>
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="notifications" className="space-y-4">
                      <div className="space-y-4">
                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={autoScanConfig.notificationConfig.onCompletion}
                            onCheckedChange={(onCompletion) => updateNotifications({ onCompletion })}
                          />
                          <Label>Notify on scan completion</Label>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={autoScanConfig.notificationConfig.onFailure}
                            onCheckedChange={(onFailure) => updateNotifications({ onFailure })}
                          />
                          <Label>Notify on scan failure</Label>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={autoScanConfig.notificationConfig.onSummary}
                            onCheckedChange={(onSummary) => updateNotifications({ onSummary })}
                          />
                          <Label>Send periodic summaries</Label>
                        </div>
                      </div>
                    </TabsContent>
                  </Tabs>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      <Separator />

      {/* Usage Sources Section */}
      <div>
        <div className="flex items-center space-x-2 mb-4">
          <Search className="h-5 w-5" />
          <h2 className="text-xl font-semibold">Usage Sources Configuration</h2>
        </div>
        <UsageSourcesSettings />
      </div>
    </div>
  );
};

export default UsageTrackingSettings;
