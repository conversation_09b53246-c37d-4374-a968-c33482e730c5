package services

import (
	"math"
	"sync"
	"time"

	"adgitops-ui/src/backend/models"
)

// ProgressBroadcaster manages real-time progress broadcasting via WebSocket
type ProgressBroadcaster struct {
	hub           *WebSocketHub
	scanProgress  map[string]*ScanProgressTracker // scanID -> progress tracker
	mutex         sync.RWMutex
	updateChannel chan models.RealTimeProgressUpdate
}

// ScanProgressTracker tracks detailed progress for a specific scan
type ScanProgressTracker struct {
	ScanID                 string
	RepoID                 string
	GroupName              string
	StartTime              time.Time
	LastUpdate             time.Time
	TotalFiles             int
	ProcessedFiles         int
	CurrentFile            string
	ChunkedConfig          *models.ChunkedProcessingConfig
	Chunks                 []models.ChunkProcessingStatus
	CurrentChunk           *models.ChunkProcessingStatus
	TotalChunks            int // Calculated upfront based on total files and chunk size
	TotalResultsFound      int // Cumulative results across all chunks
	FilesPerSecond         float64
	EstimatedTimeRemaining int64 // milliseconds
	mutex                  sync.RWMutex
}

// NewProgressBroadcaster creates a new progress broadcaster
func NewProgressBroadcaster(hub *WebSocketHub) *ProgressBroadcaster {
	pb := &ProgressBroadcaster{
		hub:           hub,
		scanProgress:  make(map[string]*ScanProgressTracker),
		updateChannel: make(chan models.RealTimeProgressUpdate, 100),
	}

	// Start the update processor
	go pb.processUpdates()

	return pb
}

// StartScan initializes progress tracking for a new scan or updates existing scan with file count
func (pb *ProgressBroadcaster) StartScan(scanID, repoID, groupName string, totalFiles int, chunkedConfig *models.ChunkedProcessingConfig) {
	pb.mutex.Lock()
	defer pb.mutex.Unlock()

	// Check if tracker already exists (from initial scan logger setup)
	existingTracker, exists := pb.scanProgress[scanID]

	// Calculate total chunks upfront if chunked processing is enabled
	totalChunks := 1 // Default to 1 chunk if not using chunked processing
	if chunkedConfig != nil && chunkedConfig.Enabled && chunkedConfig.ChunkSize > 0 && totalFiles > 0 {
		totalChunks = int(math.Ceil(float64(totalFiles) / float64(chunkedConfig.ChunkSize)))
	}

	var tracker *ScanProgressTracker

	if exists && totalFiles > 0 {
		// Update existing tracker with file count and chunked config
		existingTracker.mutex.Lock()
		existingTracker.TotalFiles = totalFiles
		existingTracker.ChunkedConfig = chunkedConfig
		existingTracker.TotalChunks = totalChunks
		existingTracker.LastUpdate = time.Now()
		existingTracker.mutex.Unlock()

		tracker = existingTracker
	} else {
		// Create new tracker
		tracker = &ScanProgressTracker{
			ScanID:            scanID,
			RepoID:            repoID,
			GroupName:         groupName,
			StartTime:         time.Now(),
			LastUpdate:        time.Now(),
			TotalFiles:        totalFiles,
			ProcessedFiles:    0,
			ChunkedConfig:     chunkedConfig,
			Chunks:            make([]models.ChunkProcessingStatus, 0),
			TotalChunks:       totalChunks,
			TotalResultsFound: 0,
		}

		pb.scanProgress[scanID] = tracker
	}

	// Broadcast scan start event
	update := models.RealTimeProgressUpdate{
		ScanID:    scanID,
		RepoID:    repoID,
		GroupName: groupName,
		Timestamp: time.Now(),
		EventType: "scan_start",
		Message:   "Scan started",
		Progress: &models.Progress{
			Current:     0,
			Total:       totalFiles,
			Percentage:  0,
			Description: "Initializing scan...",
			StartTime:   &tracker.StartTime,
		},
	}

	pb.updateChannel <- update
}

// UpdateFileProgress updates progress for file processing
func (pb *ProgressBroadcaster) UpdateFileProgress(scanID, filePath string, status string, resultsFound int, errorMsg string) {
	pb.mutex.RLock()
	tracker, exists := pb.scanProgress[scanID]
	pb.mutex.RUnlock()

	if !exists {
		return
	}

	tracker.mutex.Lock()
	defer tracker.mutex.Unlock()

	now := time.Now()
	tracker.LastUpdate = now
	tracker.CurrentFile = filePath

	if status == "completed" || status == "failed" {
		tracker.ProcessedFiles++

		// Calculate processing speed with smoothing
		elapsed := now.Sub(tracker.StartTime).Seconds()
		if elapsed > 0 {
			// Use exponential moving average for smoother speed calculation
			instantSpeed := float64(tracker.ProcessedFiles) / elapsed
			if tracker.FilesPerSecond == 0 {
				tracker.FilesPerSecond = instantSpeed
			} else {
				// Smooth the speed calculation (0.3 weight for new value, 0.7 for existing)
				tracker.FilesPerSecond = 0.7*tracker.FilesPerSecond + 0.3*instantSpeed
			}
		}

		// Estimate time remaining with improved algorithm
		if tracker.FilesPerSecond > 0 && tracker.ProcessedFiles > 0 {
			remainingFiles := tracker.TotalFiles - tracker.ProcessedFiles
			if remainingFiles > 0 {
				// Base estimate on current speed
				baseEstimate := float64(remainingFiles) / tracker.FilesPerSecond

				// Apply confidence factor based on how much we've processed
				// More processed files = higher confidence in estimate
				progressRatio := float64(tracker.ProcessedFiles) / float64(tracker.TotalFiles)
				confidenceFactor := 0.5 + 0.5*progressRatio // Range from 0.5 to 1.0

				// Adjust estimate based on confidence
				adjustedEstimate := baseEstimate / confidenceFactor

				tracker.EstimatedTimeRemaining = int64(adjustedEstimate * 1000) // Convert to milliseconds
			} else {
				tracker.EstimatedTimeRemaining = 0
			}
		}
	}

	// Update current chunk if using chunked processing
	if tracker.CurrentChunk != nil {
		if status == "completed" || status == "failed" {
			tracker.CurrentChunk.ProcessedFiles++
			if status == "completed" {
				tracker.CurrentChunk.ResultsFound += resultsFound
				// Update cumulative results count
				tracker.TotalResultsFound += resultsFound
			} else {
				tracker.CurrentChunk.ErrorCount++
			}
		}
	} else if status == "completed" {
		// Update cumulative results even when not using chunked processing
		tracker.TotalResultsFound += resultsFound
	}

	// Create progress update
	percentage := 0
	if tracker.TotalFiles > 0 {
		percentage = int((float64(tracker.ProcessedFiles) / float64(tracker.TotalFiles)) * 100)
	}

	progress := &models.Progress{
		Current:                tracker.ProcessedFiles,
		Total:                  tracker.TotalFiles,
		Percentage:             percentage,
		Description:            "Processing files...",
		CurrentFile:            filePath,
		TotalResultsFound:      tracker.TotalResultsFound,
		FilesPerSecond:         tracker.FilesPerSecond,
		EstimatedTimeRemaining: &tracker.EstimatedTimeRemaining,
		StartTime:              &tracker.StartTime,
		LastUpdateTime:         &now,
	}

	if tracker.ChunkedConfig != nil && tracker.ChunkedConfig.Enabled {
		progress.CurrentChunk = tracker.CurrentChunk.ChunkIndex
		progress.TotalChunks = tracker.TotalChunks // Use calculated total instead of dynamic count
		progress.ChunkSize = tracker.ChunkedConfig.ChunkSize
	}

	update := models.RealTimeProgressUpdate{
		ScanID:       scanID,
		RepoID:       tracker.RepoID,
		GroupName:    tracker.GroupName,
		Timestamp:    now,
		EventType:    "file_progress",
		Message:      "Processing file: " + filePath,
		Progress:     progress,
		CurrentChunk: tracker.CurrentChunk,
	}

	if errorMsg != "" {
		update.Error = errorMsg
		update.EventType = "file_error"
	}

	pb.updateChannel <- update
}

// StartChunk starts processing a new chunk
func (pb *ProgressBroadcaster) StartChunk(scanID string, chunkIndex, totalFiles int, files []string) {
	pb.mutex.RLock()
	tracker, exists := pb.scanProgress[scanID]
	pb.mutex.RUnlock()

	if !exists {
		return
	}

	tracker.mutex.Lock()
	defer tracker.mutex.Unlock()

	now := time.Now()
	chunk := models.ChunkProcessingStatus{
		ChunkIndex:     chunkIndex,
		TotalFiles:     totalFiles,
		ProcessedFiles: 0,
		Status:         "processing",
		StartTime:      &now,
		ResultsFound:   0,
		ErrorCount:     0,
	}

	// Add to chunks list or update existing
	if chunkIndex >= len(tracker.Chunks) {
		tracker.Chunks = append(tracker.Chunks, chunk)
	} else {
		tracker.Chunks[chunkIndex] = chunk
	}

	tracker.CurrentChunk = &tracker.Chunks[chunkIndex]

	update := models.RealTimeProgressUpdate{
		ScanID:       scanID,
		RepoID:       tracker.RepoID,
		GroupName:    tracker.GroupName,
		Timestamp:    now,
		EventType:    "chunk_start",
		Message:      "Starting chunk processing",
		CurrentChunk: tracker.CurrentChunk,
	}

	pb.updateChannel <- update
}

// CompleteChunk marks a chunk as completed
func (pb *ProgressBroadcaster) CompleteChunk(scanID string, chunkIndex int) {
	pb.mutex.RLock()
	tracker, exists := pb.scanProgress[scanID]
	pb.mutex.RUnlock()

	if !exists {
		return
	}

	tracker.mutex.Lock()
	defer tracker.mutex.Unlock()

	if chunkIndex < len(tracker.Chunks) {
		now := time.Now()
		tracker.Chunks[chunkIndex].Status = "completed"
		tracker.Chunks[chunkIndex].EndTime = &now

		if tracker.Chunks[chunkIndex].StartTime != nil {
			duration := now.Sub(*tracker.Chunks[chunkIndex].StartTime).Milliseconds()
			tracker.Chunks[chunkIndex].Duration = &duration
		}

		update := models.RealTimeProgressUpdate{
			ScanID:       scanID,
			RepoID:       tracker.RepoID,
			GroupName:    tracker.GroupName,
			Timestamp:    now,
			EventType:    "chunk_complete",
			Message:      "Chunk processing completed",
			CurrentChunk: &tracker.Chunks[chunkIndex],
		}

		pb.updateChannel <- update
	}
}

// CompleteScan marks a scan as completed and cleans up tracking
func (pb *ProgressBroadcaster) CompleteScan(scanID string, totalResults int) {
	pb.mutex.Lock()
	tracker, exists := pb.scanProgress[scanID]
	if exists {
		delete(pb.scanProgress, scanID)
	}
	pb.mutex.Unlock()

	if !exists {
		return
	}

	now := time.Now()

	update := models.RealTimeProgressUpdate{
		ScanID:    scanID,
		RepoID:    tracker.RepoID,
		GroupName: tracker.GroupName,
		Timestamp: now,
		EventType: "scan_complete",
		Message:   "Scan completed successfully",
		Progress: &models.Progress{
			Current:     tracker.TotalFiles,
			Total:       tracker.TotalFiles,
			Percentage:  100,
			Description: "Scan completed",
			StartTime:   &tracker.StartTime,
		},
	}

	pb.updateChannel <- update
}

// FailScan marks a scan as failed
func (pb *ProgressBroadcaster) FailScan(scanID string, errorMsg string) {
	pb.mutex.Lock()
	tracker, exists := pb.scanProgress[scanID]
	if exists {
		delete(pb.scanProgress, scanID)
	}
	pb.mutex.Unlock()

	if !exists {
		return
	}

	update := models.RealTimeProgressUpdate{
		ScanID:    scanID,
		RepoID:    tracker.RepoID,
		GroupName: tracker.GroupName,
		Timestamp: time.Now(),
		EventType: "scan_error",
		Message:   "Scan failed",
		Error:     errorMsg,
	}

	pb.updateChannel <- update
}

// processUpdates processes queued updates and broadcasts them
func (pb *ProgressBroadcaster) processUpdates() {
	for update := range pb.updateChannel {
		// Broadcast to scan-specific subscribers
		pb.hub.BroadcastToScanSubscribers(update.ScanID, update)

		// Broadcast to admin subscribers (scheduler dashboard)
		pb.hub.BroadcastToAdminSubscribers(update)

		// Broadcast to repository-specific subscribers
		if update.RepoID != "" {
			pb.hub.BroadcastToRepoSubscribers(update.RepoID, update)
		}

		// Broadcast to group-specific subscribers
		if update.GroupName != "" {
			pb.hub.BroadcastToGroupSubscribers(update.GroupName, update)
		}

		// Also broadcast to all clients for general monitoring (fallback)
		pb.hub.BroadcastProgressUpdate(update)
	}
}

// GetScanProgress returns current progress for a scan
func (pb *ProgressBroadcaster) GetScanProgress(scanID string) *ScanProgressTracker {
	pb.mutex.RLock()
	defer pb.mutex.RUnlock()

	if tracker, exists := pb.scanProgress[scanID]; exists {
		return tracker
	}
	return nil
}

// CalculateETAWithHistory calculates ETA using historical performance data
func (pb *ProgressBroadcaster) CalculateETAWithHistory(tracker *ScanProgressTracker) int64 {
	if tracker.ProcessedFiles == 0 || tracker.TotalFiles == 0 {
		return 0
	}

	now := time.Now()
	elapsed := now.Sub(tracker.StartTime).Seconds()

	if elapsed <= 0 {
		return 0
	}

	// Calculate current processing rate
	currentRate := float64(tracker.ProcessedFiles) / elapsed

	if currentRate <= 0 {
		return 0
	}

	remainingFiles := tracker.TotalFiles - tracker.ProcessedFiles
	if remainingFiles <= 0 {
		return 0
	}

	// Base calculation
	baseETA := float64(remainingFiles) / currentRate

	// Apply various adjustment factors

	// 1. Startup overhead factor - early files might be slower due to initialization
	startupFactor := 1.0
	if tracker.ProcessedFiles < 10 {
		startupFactor = 1.2 // Add 20% for startup overhead
	}

	// 2. Progress-based confidence factor
	progressRatio := float64(tracker.ProcessedFiles) / float64(tracker.TotalFiles)
	confidenceFactor := 0.6 + 0.4*progressRatio // Range from 0.6 to 1.0

	// 3. File size variation factor (assume some files might be larger)
	variationFactor := 1.1 // Add 10% buffer for file size variation

	// 4. System load factor (conservative estimate)
	systemLoadFactor := 1.05 // Add 5% for potential system load

	// Combine all factors
	adjustedETA := baseETA * startupFactor * variationFactor * systemLoadFactor / confidenceFactor

	return int64(adjustedETA * 1000) // Convert to milliseconds
}

// UpdateETACalculation updates the ETA calculation for a tracker
func (pb *ProgressBroadcaster) UpdateETACalculation(tracker *ScanProgressTracker) {
	if tracker == nil {
		return
	}

	// Use the improved ETA calculation
	tracker.EstimatedTimeRemaining = pb.CalculateETAWithHistory(tracker)

	// Update files per second with recent performance
	now := time.Now()
	elapsed := now.Sub(tracker.StartTime).Seconds()
	if elapsed > 0 && tracker.ProcessedFiles > 0 {
		tracker.FilesPerSecond = float64(tracker.ProcessedFiles) / elapsed
	}
}
