package services

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"math/rand"
	"os"
	"path/filepath"
	"sync"
	"time"

	"adgitops-ui/src/backend/models"

	"github.com/google/uuid"
)

// AutoScanService manages automated periodic scanning
type AutoScanService struct {
	configsDir   string
	statusDir    string
	jobsDir      string
	usageScanner UsageScanner
	repoManager  interface {
		GetRepoInstances() ([]RepoInstance, error)
		GetGroupsForRepo(repoID string) ([]models.Group, error)
	}

	configs      map[string]*models.AutoScanConfig // repoID -> config
	configsMutex sync.RWMutex

	activeJobs map[string]*models.AutoScanJob // jobID -> job
	jobsMutex  sync.RWMutex

	isRunning bool
	ticker    *time.Ticker
	done      chan struct{}

	// Load balancing
	scanQueue          chan *models.AutoScanJob
	maxConcurrentScans int
	currentScans       map[string]bool // groupName:repoID -> scanning
	scansMutex         sync.RWMutex

	// Progress broadcasting
	progressBroadcaster *ProgressBroadcaster // For real-time progress updates
}

// NewAutoScanService creates a new automated scanning service
func NewAutoScanService(dataDir string, usageScanner UsageScanner) *AutoScanService {
	configsDir := filepath.Join(dataDir, "auto_scan_configs")
	statusDir := filepath.Join(dataDir, "auto_scan_status")
	jobsDir := filepath.Join(dataDir, "auto_scan_jobs")

	// Create directories if they don't exist
	os.MkdirAll(configsDir, 0755)
	os.MkdirAll(statusDir, 0755)
	os.MkdirAll(jobsDir, 0755)

	return &AutoScanService{
		configsDir:         configsDir,
		statusDir:          statusDir,
		jobsDir:            jobsDir,
		usageScanner:       usageScanner,
		configs:            make(map[string]*models.AutoScanConfig),
		activeJobs:         make(map[string]*models.AutoScanJob),
		done:               make(chan struct{}),
		scanQueue:          make(chan *models.AutoScanJob, 100),
		maxConcurrentScans: 5, // Default max concurrent scans
		currentScans:       make(map[string]bool),
	}
}

// SetRepoManager sets the repository manager
func (s *AutoScanService) SetRepoManager(repoManager interface {
	GetRepoInstances() ([]RepoInstance, error)
	GetGroupsForRepo(repoID string) ([]models.Group, error)
}) {
	s.repoManager = repoManager
}

// Start begins the automated scanning service
func (s *AutoScanService) Start() error {
	if s.isRunning {
		log.Println("Auto scan service is already running")
		return nil
	}

	// Load existing configurations
	if err := s.loadConfigs(); err != nil {
		log.Printf("Warning: Failed to load auto scan configs: %v", err)
	}

	// Start job processor workers
	for i := 0; i < s.maxConcurrentScans; i++ {
		go s.jobProcessor(i)
	}

	// Check for scheduled scans every 5 minutes
	s.ticker = time.NewTicker(5 * time.Minute)
	s.isRunning = true

	go s.run()
	log.Println("Auto scan service started")
	return nil
}

// Stop stops the automated scanning service
func (s *AutoScanService) Stop() {
	if !s.isRunning {
		return
	}

	s.isRunning = false
	close(s.done)
	s.ticker.Stop()
	close(s.scanQueue)
	log.Println("Auto scan service stopped")
}

// SetProgressBroadcaster sets the progress broadcaster for real-time updates
func (s *AutoScanService) SetProgressBroadcaster(broadcaster *ProgressBroadcaster) {
	s.progressBroadcaster = broadcaster
	log.Println("Updated progress broadcaster reference in auto scan service")
}

// run is the main loop for the auto scan service
func (s *AutoScanService) run() {
	// Run once immediately to catch up on any missed schedules
	s.checkSchedules()

	for {
		select {
		case <-s.done:
			return
		case <-s.ticker.C:
			if !s.isRunning {
				return
			}
			s.checkSchedules()
		}
	}
}

// checkSchedules checks for scheduled scans that need to run
func (s *AutoScanService) checkSchedules() {
	now := time.Now()

	s.configsMutex.RLock()
	configs := make([]*models.AutoScanConfig, 0, len(s.configs))
	for _, config := range s.configs {
		if config.Enabled {
			configs = append(configs, config)
		}
	}
	s.configsMutex.RUnlock()

	for _, config := range configs {
		if s.shouldRunScan(config, now) {
			if err := s.scheduleScansForConfig(config, now); err != nil {
				log.Printf("Error scheduling scans for config %s: %v", config.ID, err)
			}
		}
	}
}

// shouldRunScan determines if a scan should run for the given config
func (s *AutoScanService) shouldRunScan(config *models.AutoScanConfig, now time.Time) bool {
	// Check if we're in the allowed time window
	if !config.IsInTimeWindow(now) {
		return false
	}

	// Check if it's time for the next scan
	if config.NextScanTime != nil && now.Before(*config.NextScanTime) {
		return false
	}

	// Check if we haven't exceeded max scans per hour
	if config.LoadBalancing.MaxScansPerHour > 0 {
		recentJobs := s.getRecentJobs(config.RepoID, time.Hour)
		if len(recentJobs) >= config.LoadBalancing.MaxScansPerHour {
			return false
		}
	}

	return true
}

// scheduleScansForConfig schedules scans for a configuration
func (s *AutoScanService) scheduleScansForConfig(config *models.AutoScanConfig, now time.Time) error {
	var groups []models.Group
	var err error

	if config.ScanAllGroups {
		// Get all groups for the repository
		if s.repoManager == nil {
			return fmt.Errorf("repository manager not set")
		}
		groups, err = s.repoManager.GetGroupsForRepo(config.RepoID)
		if err != nil {
			return fmt.Errorf("failed to get groups for repo %s: %v", config.RepoID, err)
		}
	} else {
		// Use specified target groups
		for _, groupName := range config.TargetGroups {
			groups = append(groups, models.Group{Groupname: groupName})
		}
	}

	// Schedule jobs with load balancing
	scheduledTime := now
	for i, group := range groups {
		// Spread scans across time if load balancing is enabled
		if config.LoadBalancing.SpreadAcrossDay && len(groups) > 1 {
			// Add random offset to spread scans
			maxOffset := time.Duration(24) * time.Hour / time.Duration(len(groups))
			offset := time.Duration(rand.Int63n(int64(maxOffset)))
			scheduledTime = now.Add(offset)
		} else if config.LoadBalancing.MinIntervalBetween > 0 {
			// Add minimum interval between scans
			scheduledTime = now.Add(time.Duration(i) * config.LoadBalancing.MinIntervalBetween)
		}

		job := &models.AutoScanJob{
			ID:           uuid.New().String(),
			ConfigID:     config.ID,
			RepoID:       config.RepoID,
			GroupName:    group.Groupname,
			ScheduledFor: scheduledTime,
			Status:       "pending",
			CreatedAt:    now,
		}

		// Add job to queue
		select {
		case s.scanQueue <- job:
			s.jobsMutex.Lock()
			s.activeJobs[job.ID] = job
			s.jobsMutex.Unlock()
			log.Printf("Scheduled auto scan job %s for group %s in repo %s at %s",
				job.ID, job.GroupName, job.RepoID, job.ScheduledFor.Format(time.RFC3339))
		default:
			log.Printf("Warning: Scan queue is full, skipping job for group %s", group.Groupname)
		}
	}

	// Update next scan time
	nextScan := config.CalculateNextScanTime(now)
	config.NextScanTime = &nextScan
	config.LastScanTime = &now

	// Save updated config
	if err := s.saveConfig(config); err != nil {
		log.Printf("Warning: Failed to save updated config %s: %v", config.ID, err)
	}

	return nil
}

// jobProcessor processes scan jobs from the queue
func (s *AutoScanService) jobProcessor(workerID int) {
	log.Printf("Auto scan job processor %d started", workerID)

	for job := range s.scanQueue {
		if !s.isRunning {
			break
		}

		// Wait until scheduled time
		if time.Now().Before(job.ScheduledFor) {
			time.Sleep(time.Until(job.ScheduledFor))
		}

		// Check if we can run this scan (not already scanning this group)
		scanKey := fmt.Sprintf("%s:%s", job.GroupName, job.RepoID)
		s.scansMutex.Lock()
		if s.currentScans[scanKey] {
			s.scansMutex.Unlock()
			log.Printf("Skipping auto scan for %s - already in progress", scanKey)
			job.Status = "skipped"
			continue
		}
		s.currentScans[scanKey] = true
		s.scansMutex.Unlock()

		// Process the job
		s.processJob(job, workerID)

		// Mark scan as complete
		s.scansMutex.Lock()
		delete(s.currentScans, scanKey)
		s.scansMutex.Unlock()
	}

	log.Printf("Auto scan job processor %d stopped", workerID)
}

// processJob processes a single scan job
func (s *AutoScanService) processJob(job *models.AutoScanJob, workerID int) {
	startTime := time.Now()
	job.Status = "running"
	job.StartedAt = &startTime

	log.Printf("Worker %d: Starting auto scan for group %s in repo %s",
		workerID, job.GroupName, job.RepoID)

	// Create scan request
	request := models.UsageScanRequest{
		GroupName: job.GroupName,
		RepoID:    job.RepoID,
		Force:     false, // Don't force if already scanning
	}

	// Execute the scan with a more reasonable timeout (10 minutes)
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()

	err := s.usageScanner.ScanGroupUsage(ctx, request)

	completedTime := time.Now()
	job.CompletedAt = &completedTime

	if err != nil {
		job.Status = "failed"
		job.Error = err.Error()
		log.Printf("Worker %d: Auto scan failed for group %s in repo %s: %v",
			workerID, job.GroupName, job.RepoID, err)
	} else {
		job.Status = "completed"

		// Get results count
		if results, err := s.usageScanner.GetUsageResults(job.GroupName, job.RepoID, 1, 1); err == nil {
			job.ResultsCount = results.Total
		}

		log.Printf("Worker %d: Auto scan completed for group %s in repo %s in %v",
			workerID, job.GroupName, job.RepoID, completedTime.Sub(startTime))
	}

	// Save job status
	if err := s.saveJob(job); err != nil {
		log.Printf("Warning: Failed to save job %s: %v", job.ID, err)
	}
}

// Configuration management methods
func (s *AutoScanService) CreateConfig(config *models.AutoScanConfig) error {
	if err := config.Validate(); err != nil {
		return err
	}

	config.ID = uuid.New().String()
	config.CreatedAt = time.Now()
	config.UpdatedAt = time.Now()

	// Calculate initial next scan time
	nextScan := config.CalculateNextScanTime(time.Now())
	config.NextScanTime = &nextScan

	s.configsMutex.Lock()
	s.configs[config.RepoID] = config
	s.configsMutex.Unlock()

	return s.saveConfig(config)
}

// Helper methods for persistence
func (s *AutoScanService) loadConfigs() error {
	files, err := ioutil.ReadDir(s.configsDir)
	if err != nil {
		return err
	}

	for _, file := range files {
		if filepath.Ext(file.Name()) == ".json" {
			configPath := filepath.Join(s.configsDir, file.Name())
			data, err := ioutil.ReadFile(configPath)
			if err != nil {
				log.Printf("Warning: Failed to read config file %s: %v", configPath, err)
				continue
			}

			var config models.AutoScanConfig
			if err := json.Unmarshal(data, &config); err != nil {
				log.Printf("Warning: Failed to parse config file %s: %v", configPath, err)
				continue
			}

			s.configsMutex.Lock()
			s.configs[config.RepoID] = &config
			s.configsMutex.Unlock()
		}
	}

	return nil
}

func (s *AutoScanService) saveConfig(config *models.AutoScanConfig) error {
	config.UpdatedAt = time.Now()

	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return err
	}

	configPath := filepath.Join(s.configsDir, fmt.Sprintf("%s.json", config.RepoID))
	return ioutil.WriteFile(configPath, data, 0644)
}

func (s *AutoScanService) saveJob(job *models.AutoScanJob) error {
	data, err := json.MarshalIndent(job, "", "  ")
	if err != nil {
		return err
	}

	jobPath := filepath.Join(s.jobsDir, fmt.Sprintf("%s.json", job.ID))
	return ioutil.WriteFile(jobPath, data, 0644)
}

func (s *AutoScanService) getRecentJobs(repoID string, duration time.Duration) []*models.AutoScanJob {
	// Implementation would scan job files and return recent jobs
	// For brevity, returning empty slice
	return []*models.AutoScanJob{}
}
