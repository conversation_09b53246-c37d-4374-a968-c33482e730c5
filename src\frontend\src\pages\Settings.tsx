import { useEffect, useState } from "react"
import { useSearchParams, useNavigate } from "react-router-dom"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Database, AlertTriangle } from "lucide-react"
import RepositorySettings from "@/components/settings/RepositorySettings"
import SchedulerDashboard from "@/pages/Settings/SchedulerDashboard"
import UsageSourcesSettings from "@/components/settings/UsageSourcesSettings"

const Settings = () => {
  const [searchParams, setSearchParams] = useSearchParams()
  const navigate = useNavigate()
  const [activeTab, setActiveTab] = useState("general")

  // Get tab from URL parameter
  useEffect(() => {
    const tab = searchParams.get("tab")
    if (tab && ["general", "repository", "scheduler", "usage-sources", "storage"].includes(tab)) {
      setActiveTab(tab)
    }
  }, [searchParams])

  // Update URL when tab changes
  const handleTabChange = (value: string) => {
    setActiveTab(value)
    setSearchParams({ tab: value })
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Settings</h1>
      </div>

      <Tabs value={activeTab} onValueChange={handleTabChange}>
        <TabsList className="mb-4">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="repository">Repository Integration</TabsTrigger>
          <TabsTrigger value="scheduler">Scheduler Dashboard</TabsTrigger>
          <TabsTrigger value="usage-sources">Usage Sources</TabsTrigger>
          <TabsTrigger value="storage">Storage Architecture</TabsTrigger>
        </TabsList>

        <TabsContent value="general">
          <Card>
            <CardHeader>
              <CardTitle>General Settings</CardTitle>
              <CardDescription>
                Configure general application settings.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="appName">Application Name</Label>
                  <Input id="appName" defaultValue="ADGitOps UI" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="dataDirectory">Data Directory</Label>
                  <Input id="dataDirectory" defaultValue="./data" />
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button>Save Changes</Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="repository">
          <RepositorySettings />
        </TabsContent>

        <TabsContent value="scheduler">
          <SchedulerDashboard />
        </TabsContent>

        <TabsContent value="usage-sources">
          <UsageSourcesSettings />
        </TabsContent>

        <TabsContent value="storage">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Database className="h-5 w-5 text-purple-600" />
                <span>Persistent Storage Architecture</span>
                <Badge variant="outline" className="bg-orange-100 text-orange-800 border-orange-300">
                  <AlertTriangle className="h-3 w-3 mr-1" />
                  Not Implemented
                </Badge>
              </CardTitle>
              <CardDescription>
                Future data storage solution for scan history, logs, and results
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Architecture Overview */}
                <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                  <h3 className="font-medium text-purple-800 mb-3">Proposed Architecture</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium text-purple-700 mb-2">Vector Database (Primary)</h4>
                      <ul className="text-sm text-purple-600 space-y-1">
                        <li>• Chroma/Pinecone for semantic search</li>
                        <li>• Store scan results with embeddings</li>
                        <li>• Fast similarity-based queries</li>
                        <li>• Automatic clustering of related results</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-medium text-purple-700 mb-2">Time-Series Database</h4>
                      <ul className="text-sm text-purple-600 space-y-1">
                        <li>• InfluxDB for metrics and logs</li>
                        <li>• Efficient time-based queries</li>
                        <li>• Automatic data retention policies</li>
                        <li>• Real-time monitoring dashboards</li>
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Implementation Status */}
                <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <AlertTriangle className="h-5 w-5 text-orange-600" />
                    <h3 className="font-medium text-orange-800">Implementation Status</h3>
                  </div>
                  <p className="text-sm text-orange-700 mb-3">
                    This storage architecture is currently in the design phase and has not been implemented yet.
                  </p>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-orange-700">Vector Database Integration</span>
                      <Badge variant="outline" className="text-orange-600 border-orange-300">Planned</Badge>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-orange-700">Time-Series Database</span>
                      <Badge variant="outline" className="text-orange-600 border-orange-300">Planned</Badge>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-orange-700">Data Migration Tools</span>
                      <Badge variant="outline" className="text-orange-600 border-orange-300">Planned</Badge>
                    </div>
                  </div>
                </div>

                {/* Future Benefits */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h3 className="font-medium text-blue-800 mb-3">Expected Benefits</h3>
                  <ul className="text-sm text-blue-700 space-y-1">
                    <li>• Improved query performance for large datasets</li>
                    <li>• Semantic search capabilities for scan results</li>
                    <li>• Automatic data archiving and retention</li>
                    <li>• Real-time analytics and monitoring</li>
                    <li>• Scalable storage for growing data volumes</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Export settings tab removed as it's now covered by the Report feature */}
      </Tabs>
    </div>
  )
}

export default Settings
