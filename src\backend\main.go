package main

import (
	"flag"
	"log"
	"os"
	"path/filepath"

	"adgitops-ui/src/backend/api"
	"adgitops-ui/src/backend/controllers"
	"adgitops-ui/src/backend/repository"
	"adgitops-ui/src/backend/services"
)

const (
	defaultPort        = "8080"
	defaultReportsDir  = "reports"
	defaultReposDir    = "../../repos"
	defaultConfigsFile = "configs/repositories.json" // Path relative to project root
	defaultPollSeconds = 300                         // 5 minutes
	defaultStaticDir   = "static"                    // Default static directory for frontend
	defaultDataDir     = "data"                      // Default directory for data files
)

func main() {
	// Parse command line flags
	port := flag.String("port", defaultPort, "API server port")
	reportsDir := flag.String("reports-dir", defaultReportsDir, "Directory for report files")
	reposDir := flag.String("repos-dir", defaultReposDir, "Base directory for repository clones")
	configsFile := flag.String("configs-file", defaultConfigsFile, "Path to repositories configuration file")
	staticDir := flag.String("static-dir", defaultStaticDir, "Directory containing frontend static files")
	dataDir := flag.String("data-dir", defaultDataDir, "Directory for data files")
	flag.Parse()

	// Get executable directory to use for resolving relative paths
	execDir, err := os.Executable()
	if err != nil {
		log.Printf("Warning: Could not determine executable path: %v", err)
		execDir = "."
	} else {
		execDir = filepath.Dir(execDir)
	}

	// Convert relative paths to absolute if needed
	if !filepath.IsAbs(*configsFile) {
		absConfigsPath, err := filepath.Abs(*configsFile)
		if err == nil {
			*configsFile = absConfigsPath
		}
	}

	log.Printf("Using configurations file: %s", *configsFile)

	// Check if configuration file exists and log result
	if _, err := os.Stat(*configsFile); os.IsNotExist(err) {
		log.Printf("Warning: Configuration file does not exist: %s", *configsFile)
	} else if err != nil {
		log.Printf("Warning: Error checking configuration file: %v", err)
	} else {
		log.Printf("Configuration file exists: %s", *configsFile)
	}

	// Create necessary directories
	if err := os.MkdirAll(*reportsDir, 0755); err != nil {
		log.Fatalf("Failed to create reports directory: %v", err)
	}

	if err := os.MkdirAll(*reposDir, 0755); err != nil {
		log.Fatalf("Failed to create repositories directory: %v", err)
	}

	if err := os.MkdirAll(filepath.Dir(*configsFile), 0755); err != nil {
		log.Fatalf("Failed to create configurations directory: %v", err)
	}

	// Initialize services
	syncLogger := services.NewSyncLogger()
	dataProcessor := services.NewDataProcessor(*reportsDir)

	// Run migration to update reports with shared preset IDs
	log.Println("Running migration to update reports with shared preset IDs...")
	if err := dataProcessor.MigrateReportsSharedPresetIDs(); err != nil {
		log.Printf("Warning: Failed to migrate reports with shared preset IDs: %v", err)
	} else {
		log.Println("Migration completed successfully")
	}

	// Initialize scheduler service first
	schedulerService := services.NewSchedulerService(dataProcessor, nil, *dataDir)

	// Initialize repository manager with sync logger
	repoManager := repository.NewRepositoryManager(*configsFile, syncLogger)

	// Update scheduler with repo manager
	schedulerService.SetRepoManager(repoManager)

	// Load existing configurations
	if err := repoManager.LoadConfigurations(); err != nil {
		log.Printf("Warning: Failed to load configurations: %v", err)
	}

	// Register callbacks for repository changes
	repos, err := repoManager.GetRepositories()
	if err != nil {
		log.Printf("Warning: Failed to get repositories: %v", err)
	} else {
		for _, repo := range repos {
			repoID := repo.GetConfig().ID
			log.Printf("Registering change callback for repository: %s", repoID)

			// Register a callback that triggers a sync when changes are detected
			if err := repoManager.RegisterChangeCallback(repoID, func() {
				log.Printf("Change detected in repository %s, triggering sync", repoID)
				// Get the repository again to ensure we have the latest instance
				currentRepo, err := repoManager.GetRepository(repoID)
				if err != nil {
					log.Printf("Error getting repository %s for sync: %v", repoID, err)
					return
				}

				// Trigger a sync in a separate goroutine
				go func() {
					log.Printf("Starting sync for repository %s due to detected changes", repoID)
					if err := currentRepo.SyncRepository(); err != nil {
						log.Printf("Error syncing repository %s: %v", repoID, err)
					} else {
						log.Printf("Successfully synced repository %s after detecting changes", repoID)
					}
				}()
			}); err != nil {
				log.Printf("Warning: Failed to register change callback for repository %s: %v", repoID, err)
			}
		}
	}

	// Start the scheduler service
	if err := schedulerService.Start(); err != nil {
		log.Printf("Warning: Failed to start scheduler service: %v", err)
	}

	// Initialize controllers
	repoController := controllers.NewRepositoryController(repoManager, syncLogger)
	dataController := controllers.NewDataController(dataProcessor, repoManager, schedulerService)

	// Initialize WebSocket infrastructure for real-time progress updates
	wsHub := services.NewWebSocketHub()
	go wsHub.Run() // Start the WebSocket hub in a goroutine

	// Initialize progress broadcaster
	progressBroadcaster := services.NewProgressBroadcaster(wsHub)

	// Initialize usage services
	sourceManager := services.NewUsageSourceManager(*dataDir)
	scanLogger := services.NewScanLogger()

	// Set progress broadcaster on scan logger for real-time updates
	scanLogger.SetProgressBroadcaster(progressBroadcaster)

	// Set progress broadcaster on scheduler service for real-time monitoring
	schedulerService.SetProgressBroadcaster(progressBroadcaster)

	usageScanner := services.NewUsageScannerService(sourceManager, *dataDir, scanLogger)

	// Start the usage scanner service (which includes the worker pool)
	if err := usageScanner.Start(); err != nil {
		log.Printf("Warning: Failed to start usage scanner service: %v", err)
	}

	// Initialize scheduler controller (with usage scanner now available)
	schedulerController := controllers.NewSchedulerController(
		schedulerService,
		usageScanner, // usageScanner - now properly initialized
		nil,          // autoScanService - TODO: Initialize when auto scan is fully implemented
		nil,          // workerPool - TODO: Initialize when worker pool is fully implemented
		dataProcessor,
		scanLogger, // scanLogger - for completed tasks functionality
	)

	// Initialize usage controller
	usageController := controllers.NewUsageController(sourceManager, usageScanner, scanLogger)

	// Initialize search controller
	log.Printf("Initializing search controller with data directory: %s", *dataDir)
	searchController, err := controllers.NewBleveSearchController(dataController, *dataDir)
	if err != nil {
		log.Printf("Warning: Failed to initialize search controller: %v", err)
		log.Printf("Continuing without search functionality")
	} else {
		log.Printf("Search controller initialized successfully")

		// Set the search controller on the repository manager to enable automatic reindexing after sync
		log.Printf("Setting search controller on repository manager for automatic reindexing")
		repoManager.SetSearchController(searchController)
	}

	// Create API server
	server := api.NewServer(*port)
	server.AddController(repoController)
	server.AddController(dataController)
	server.AddController(schedulerController)
	server.AddController(usageController)

	// Add search controller if initialized
	if searchController != nil {
		server.AddController(searchController)
		defer searchController.Close()
	}

	// Set WebSocket hub for real-time progress updates
	server.SetWebSocketHub(wsHub)

	// Set static directory for frontend files
	server.SetStaticDir(*staticDir)

	// Start API server
	log.Printf("Starting server on port %s", *port)
	log.Fatal(server.Start())
}
