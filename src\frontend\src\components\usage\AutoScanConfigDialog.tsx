import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Settings,
  Clock,
  Users,
  Bell,
  Save,
  AlertCircle,
  Info,
  Calendar,
  Timer
} from 'lucide-react';

import type {
  AutoScanConfig,
  AutoScanStatus,
  TimeWindow,
  LoadBalancingConfig,
  NotificationConfig
} from '@/types/autoScan';
import {
  DEFAULT_AUTO_SCAN_CONFIG,
  formatTimeWindow,
  validateAutoScanConfig,
  isInTimeWindow
} from '@/types/autoScan';

interface AutoScanConfigDialogProps {
  repoId: string;
  trigger: React.ReactNode;
  config?: AutoScanConfig;
  status?: AutoScanStatus;
  onSave: (config: AutoScanConfig) => Promise<void>;
  availableGroups: string[];
}

const AutoScanConfigDialog: React.FC<AutoScanConfigDialogProps> = ({
  repoId,
  trigger,
  config,
  status,
  onSave,
  availableGroups
}) => {
  const [open, setOpen] = useState(false);
  const [formData, setFormData] = useState<Partial<AutoScanConfig>>({
    ...DEFAULT_AUTO_SCAN_CONFIG,
    repoId,
    ...config
  });
  const [errors, setErrors] = useState<string[]>([]);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (config) {
      setFormData({ ...config });
    }
  }, [config]);

  const handleSave = async () => {
    const validationErrors = validateAutoScanConfig(formData);
    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      return;
    }

    setSaving(true);
    try {
      await onSave(formData as AutoScanConfig);
      setOpen(false);
      setErrors([]);
    } catch (error) {
      setErrors([`Failed to save configuration: ${error}`]);
    } finally {
      setSaving(false);
    }
  };

  const updateFormData = (updates: Partial<AutoScanConfig>) => {
    setFormData(prev => ({ ...prev, ...updates }));
    setErrors([]);
  };

  const updateTimeWindow = (updates: Partial<TimeWindow>) => {
    setFormData(prev => ({
      ...prev,
      timeWindow: { ...prev.timeWindow!, ...updates }
    }));
  };

  const updateLoadBalancing = (updates: Partial<LoadBalancingConfig>) => {
    setFormData(prev => ({
      ...prev,
      loadBalancing: { ...prev.loadBalancing!, ...updates }
    }));
  };

  const updateNotifications = (updates: Partial<NotificationConfig>) => {
    setFormData(prev => ({
      ...prev,
      notificationConfig: { ...prev.notificationConfig!, ...updates }
    }));
  };

  const timezones = [
    'UTC',
    'America/New_York',
    'America/Chicago',
    'America/Denver',
    'America/Los_Angeles',
    'Europe/London',
    'Europe/Paris',
    'Asia/Tokyo',
    'Asia/Shanghai',
    'Australia/Sydney'
  ];

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Settings className="h-5 w-5 mr-2" />
            {config ? 'Edit' : 'Configure'} Automated Scanning
            {status && (
              <Badge 
                variant={status.isActive ? "default" : "secondary"} 
                className="ml-2"
              >
                {status.isActive ? 'Active' : 'Inactive'}
              </Badge>
            )}
          </DialogTitle>
        </DialogHeader>

        {errors.length > 0 && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
            <div className="flex items-start">
              <AlertCircle className="h-4 w-4 text-red-500 mr-2 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-red-700">Configuration errors:</p>
                <ul className="text-sm text-red-600 mt-1 list-disc list-inside">
                  {errors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        )}

        <Tabs defaultValue="basic" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="basic">Basic</TabsTrigger>
            <TabsTrigger value="schedule">Schedule</TabsTrigger>
            <TabsTrigger value="groups">Groups</TabsTrigger>
            <TabsTrigger value="advanced">Advanced</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="enabled">Enable Automated Scanning</Label>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="enabled"
                    checked={formData.enabled || false}
                    onCheckedChange={(enabled) => updateFormData({ enabled })}
                  />
                  <span className="text-sm text-muted-foreground">
                    {formData.enabled ? 'Enabled' : 'Disabled'}
                  </span>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="frequency">Scan Frequency</Label>
                <Select
                  value={formData.frequency || 'daily'}
                  onValueChange={(frequency: 'daily' | 'weekly' | 'monthly') => 
                    updateFormData({ frequency })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="daily">Daily</SelectItem>
                    <SelectItem value="weekly">Weekly</SelectItem>
                    <SelectItem value="monthly">Monthly</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="maxConcurrentScans">Max Concurrent Scans</Label>
                <Input
                  id="maxConcurrentScans"
                  type="number"
                  min="1"
                  max="10"
                  value={formData.maxConcurrentScans || 3}
                  onChange={(e) => updateFormData({ 
                    maxConcurrentScans: parseInt(e.target.value) || 3 
                  })}
                />
              </div>
            </div>

            {status && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <div className="flex items-start">
                  <Info className="h-4 w-4 text-blue-500 mr-2 mt-0.5" />
                  <div className="text-sm">
                    <p className="font-medium text-blue-700">Current Status</p>
                    <div className="grid grid-cols-2 gap-4 mt-2 text-blue-600">
                      <div>Total Scans: {status.totalScansRun}</div>
                      <div>Success Rate: {status.totalScansRun > 0 ? 
                        Math.round((status.successfulScans / status.totalScansRun) * 100) : 0}%</div>
                      <div>Currently Scanning: {status.currentlyScanning.length} groups</div>
                      <div>Next Scan: {status.nextScanTime ? 
                        new Date(status.nextScanTime).toLocaleString() : 'Not scheduled'}</div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="schedule" className="space-y-4">
            <div className="space-y-4">
              <div>
                <Label className="text-base font-medium flex items-center mb-3">
                  <Clock className="h-4 w-4 mr-2" />
                  Time Window
                </Label>
                <div className="grid grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="startHour">Start Hour</Label>
                    <Select
                      value={formData.timeWindow?.startHour?.toString() || '2'}
                      onValueChange={(value) => updateTimeWindow({ startHour: parseInt(value) })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Array.from({ length: 24 }, (_, i) => (
                          <SelectItem key={i} value={i.toString()}>
                            {i.toString().padStart(2, '0')}:00
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="endHour">End Hour</Label>
                    <Select
                      value={formData.timeWindow?.endHour?.toString() || '6'}
                      onValueChange={(value) => updateTimeWindow({ endHour: parseInt(value) })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Array.from({ length: 24 }, (_, i) => (
                          <SelectItem key={i} value={i.toString()}>
                            {i.toString().padStart(2, '0')}:00
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="timezone">Timezone</Label>
                    <Select
                      value={formData.timeWindow?.timezone || 'UTC'}
                      onValueChange={(timezone) => updateTimeWindow({ timezone })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {timezones.map((tz) => (
                          <SelectItem key={tz} value={tz}>
                            {tz}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {formData.timeWindow && (
                  <div className="mt-2 text-sm text-muted-foreground">
                    Scans will run between {formatTimeWindow(formData.timeWindow)}
                    {isInTimeWindow(formData as AutoScanConfig) && (
                      <Badge variant="secondary" className="ml-2 text-xs">
                        Currently in window
                      </Badge>
                    )}
                  </div>
                )}
              </div>

              <div>
                <Label className="text-base font-medium flex items-center mb-3">
                  <Timer className="h-4 w-4 mr-2" />
                  Load Balancing
                </Label>
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={formData.loadBalancing?.spreadAcrossDay || false}
                      onCheckedChange={(spreadAcrossDay) => 
                        updateLoadBalancing({ spreadAcrossDay })
                      }
                    />
                    <Label>Spread scans across the day</Label>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="minInterval">Min Interval Between Scans (minutes)</Label>
                      <Input
                        id="minInterval"
                        type="number"
                        min="1"
                        value={formData.loadBalancing?.minIntervalBetween || 5}
                        onChange={(e) => updateLoadBalancing({ 
                          minIntervalBetween: parseInt(e.target.value) || 5 
                        })}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="maxPerHour">Max Scans Per Hour</Label>
                      <Input
                        id="maxPerHour"
                        type="number"
                        min="1"
                        value={formData.loadBalancing?.maxScansPerHour || 10}
                        onChange={(e) => updateLoadBalancing({ 
                          maxScansPerHour: parseInt(e.target.value) || 10 
                        })}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="groups" className="space-y-4">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  checked={formData.scanAllGroups || false}
                  onCheckedChange={(scanAllGroups) => updateFormData({ scanAllGroups })}
                />
                <Label>Scan all groups in repository</Label>
              </div>

              {!formData.scanAllGroups && (
                <div className="space-y-2">
                  <Label>Target Groups</Label>
                  <div className="border rounded-lg p-3 max-h-48 overflow-y-auto">
                    {availableGroups.length === 0 ? (
                      <p className="text-sm text-muted-foreground">No groups available</p>
                    ) : (
                      <div className="space-y-2">
                        {availableGroups.map((group) => (
                          <div key={group} className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              id={`group-${group}`}
                              checked={formData.targetGroups?.includes(group) || false}
                              onChange={(e) => {
                                const currentGroups = formData.targetGroups || [];
                                if (e.target.checked) {
                                  updateFormData({ 
                                    targetGroups: [...currentGroups, group] 
                                  });
                                } else {
                                  updateFormData({ 
                                    targetGroups: currentGroups.filter(g => g !== group) 
                                  });
                                }
                              }}
                              className="rounded"
                            />
                            <Label htmlFor={`group-${group}`} className="text-sm">
                              {group}
                            </Label>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                  {formData.targetGroups && formData.targetGroups.length > 0 && (
                    <p className="text-sm text-muted-foreground">
                      {formData.targetGroups.length} group{formData.targetGroups.length !== 1 ? 's' : ''} selected
                    </p>
                  )}
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="advanced" className="space-y-4">
            <div>
              <Label className="text-base font-medium flex items-center mb-3">
                <Bell className="h-4 w-4 mr-2" />
                Notifications
              </Label>
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={formData.notificationConfig?.onCompletion || false}
                    onCheckedChange={(onCompletion) => 
                      updateNotifications({ onCompletion })
                    }
                  />
                  <Label>Notify on scan completion</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    checked={formData.notificationConfig?.onFailure || false}
                    onCheckedChange={(onFailure) => 
                      updateNotifications({ onFailure })
                    }
                  />
                  <Label>Notify on scan failure</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    checked={formData.notificationConfig?.onSummary || false}
                    onCheckedChange={(onSummary) => 
                      updateNotifications({ onSummary })
                    }
                  />
                  <Label>Send periodic summaries</Label>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={saving}>
            {saving ? (
              <>
                <Timer className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Configuration
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AutoScanConfigDialog;
